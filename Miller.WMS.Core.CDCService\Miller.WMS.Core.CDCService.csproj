<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-Miller.WMS.Core.CDCService-50cbad58-452a-4aba-ac6c-5622e2a0e443</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Aspire.Elastic.Clients.Elasticsearch" Version="9.2.1-preview.1.25222.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Miller.WMS.Data\Miller.WMS.Data.csproj" />
    <ProjectReference Include="..\Miller.WMS.Domain\Miller.WMS.Domain.csproj" />
    <ProjectReference Include="..\Miller.WMS.ServiceDefaults\Miller.WMS.ServiceDefaults.csproj" />
  </ItemGroup>
</Project>
