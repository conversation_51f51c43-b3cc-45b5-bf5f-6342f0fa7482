{"format": 1, "restore": {"C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Core.DataService\\Miller.WMS.Core.DataService.csproj": {}}, "projects": {"C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Core.DataService\\Miller.WMS.Core.DataService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Core.DataService\\Miller.WMS.Core.DataService.csproj", "projectName": "Miller.WMS.Core.DataService", "projectPath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Core.DataService\\Miller.WMS.Core.DataService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Core.DataService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Data\\Miller.WMS.Data.csproj": {"projectPath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Data\\Miller.WMS.Data.csproj"}, "C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj": {"projectPath": "C:\\_\\<PERSON>_G<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aspire.Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.4.0, )"}, "Bogus": {"target": "Package", "version": "[35.6.3, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Data\\Miller.WMS.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Data\\Miller.WMS.Data.csproj", "projectName": "Miller.WMS.Data", "projectPath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Data\\Miller.WMS.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Domain\\Miller.WMS.Domain.csproj": {"projectPath": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Domain\\Miller.WMS.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Domain\\Miller.WMS.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Domain\\Miller.WMS.Domain.csproj", "projectName": "Miller.WMS.Domain", "projectPath": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Domain\\Miller.WMS.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_\\<PERSON>_G<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj", "projectName": "Miller.WMS.ServiceDefaults", "projectPath": "C:\\_\\<PERSON>_G<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Http.Resilience": {"target": "Package", "version": "[9.7.0, )"}, "Microsoft.Extensions.ServiceDiscovery": {"target": "Package", "version": "[9.4.0, )"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.Runtime": {"target": "Package", "version": "[1.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}