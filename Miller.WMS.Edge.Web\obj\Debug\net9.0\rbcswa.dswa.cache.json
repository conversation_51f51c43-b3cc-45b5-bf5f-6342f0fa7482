{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["DvTCJpaqtuNfsIKgcyTzJBPcqhWjB77P94Y2LP7BWpE=", "P16OxsTm7pFxNCes3IaD2WEzAH6MW7EZlvUfLVydgVk=", "WV34hgXlv+DoeZmwjdTVt/iR1lYilwrglKz77kWIBzg=", "RHZVN9E8WTsj9gcEalNAULdsye/sPF6vPZW1L24a8ts=", "axIMHiySwc9Bd1JF6dzO2m2wWOqaGcZ84oCHNVUPpHI=", "RSzyBTuWGpb3KZEd+/WxZC+aRRgHDWSKoRFqiYU4KWM=", "JBHtWv56z/rG+dOpFin1CKb6+7uVbVj373H09KOyJKk=", "Le8hSHTsXmwR+C8TygLqULBd1JJMxqsTl1PSgF8HTLM=", "5KZqS0tM83TNkc4ZBg5zsyT9H7r5yurDWubVTlfBDBQ=", "wtuDWjSl4JpXsS4y8JfT0tIcqYvxbU1h+yDz2jbO2ic=", "xIEAKK5HEMzZZMaNmPhbyRZPdMc95gQBH0k1q9sXErY=", "5QVD6QbEv6u6FsRW5Au6nsWXFQE1nxddcq8EsMNXKxc=", "5TpOS6wXczfjpryR+V/SurHWS2YgBp+2qZQIhJ49HiE=", "du6fF75oUTakrqGrxHRwR1GdhoGlFTQYOBMuWn5L6Yg=", "7oRjm54NJ1J5Qyac692YL8+scq3HPGQL22/QK3Z/7kc=", "n7Qz6/IJCXusiFcyn/GlTsBECxW3AyhX1zDCuyxuuXs=", "w4OMw1vEiSrXdkJZ8c0J4BWQzrrLM2ldST9yZEDPmwI=", "5hBq0JXD94XidzSvfMSPSXFtLT7yhVTtRjUg8OOpDFQ=", "BACKXPwO74H0mo34HKME0kr3kPR+yuhXdNSoE/s4YiA=", "uHqT4V3WpahCjcTgf7Q2+rsDLvfhSCquKYKknJCeXpM=", "NJGac0t9S1T5oFO58BPzfqiyubTzzdNan5Yuy/+NHsQ=", "Me+m2RNz/cZK4ds3uWb53qQTXDX/29bXTViSPf4xrFA=", "xTmQ3j04WnNtgadiPbFw4ceqjMvXPw24K3jAaQJbkDY=", "2pa+3UWfVNx8z8GVf14GdyCgXE/y+M0+SGTWASQ5Po0=", "gaEDAvnwREYmEOqTVuvOjk9mHRDPLrmCqJPrJ2hOTtQ=", "u5UF9RxMVtB9vEibnBmxMty0nDqj0rVeDYxSIYTuXeE=", "X5U6HfFBZu+1ZDvgk7FrBiS2FkDDwTd9ZZB8eOQhbv4=", "jL2GyqrRxICb3LRbPkC+2W6uc/DKtztX61m2oFOorpU=", "m6+znx0rw70mN770BpXbR995P0URLfIEgxNquX+1fCU=", "0cADz9Nz3lPcCoaWPiBMCkHkVDM/wWjeyweYrv8cdXg=", "jgDrt4DhAMvMd1BsGmC9JTuTsUUUOXrtxn1377biGCM=", "UEOPDNkm3/N8WKu44bA+GCqEUdppByZLfP2Z+bbk9nM=", "inL5c/lQhzI2MuKA3ptneJHQR4wEmxxxX4PiWb4aDC4=", "iRqdraum3P1gs93sCPl1It6da8jjUJSzbbKTXf81Fek=", "65r6AasJgwCtP/dIK9eRhY+cGkmKBizy+h7S5bn6ha8=", "Oj7zKzaj4kfgdvKKB4KRIEPQMdw/lsySgmG81FYb6Ew=", "4cLpYeyyosmGYEvc0zE1U84fXY5Kh4kZLuAdtBWfjZw=", "6wkC5UTVi4F0Zi6y3RSD0cQoW3BWHa5GFTyJHL3Yvqg=", "FcGLGVkv9fbxmYuRcWMntsMxZILXJ08XejMLddo/NtI=", "YwgwVMC1VKxcjDqeYlm0MoFKrF1d8JCj8HxpvuRtseU=", "lk3dlUZCtoJBgWevT1xocYD3NQl96L5yShGApnk4dQw=", "XdyzhoEc3WOIC0hsGXg10nMspH26eriAIPXQ8TE5xs0=", "nXQxY7a4LHNQf9yVOIf4veJDN430YTRnrcFOoFZcqIk=", "5KyCv8nhEFou2zLvgzw8i5/uc45XVwE1aY+Z+JAxeeM=", "qXGHTIDyrV+Q4TBkfnTmlKw4VYbZpMz9UU0YNozbCTs=", "oGGmRgZYhI36UporiigrkQel+K2ZKBojUJTrNkqwIoM=", "KxUZubo3KM1I6bICiM9291HYgLKNj44KOaMbzKV92Sw="], "CachedAssets": {"KxUZubo3KM1I6bICiM9291HYgLKNj44KOaMbzKV92Sw=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\q4h1had4qc-ug9uonmnr1.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "Miller.WMS.Edge.Web#[.{fingerprint=ug9uonmnr1}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Edge.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkdzock9gm", "Integrity": "dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Edge.Web.bundle.scp.css", "FileLength": 1731, "LastWriteTime": "2025-08-06T21:57:59.3833144+00:00"}, "oGGmRgZYhI36UporiigrkQel+K2ZKBojUJTrNkqwIoM=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\emjngf5i2u-ug9uonmnr1.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "Miller.WMS.Edge.Web#[.{fingerprint=ug9uonmnr1}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Edge.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkdzock9gm", "Integrity": "dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Edge.Web.styles.css", "FileLength": 1731, "LastWriteTime": "2025-08-06T21:57:59.3738963+00:00"}, "qXGHTIDyrV+Q4TBkfnTmlKw4VYbZpMz9UU0YNozbCTs=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\b1bks4wm49-r37jpkscte.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=r37jpkscte}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1vtrgsyhbw", "Integrity": "E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55527, "LastWriteTime": "2025-08-06T21:57:59.4023118+00:00"}, "5KyCv8nhEFou2zLvgzw8i5/uc45XVwE1aY+Z+JAxeeM=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\9oew4imh8l-ze3dr5b7df.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=ze3dr5b7df}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2kg58l0pnq", "Integrity": "Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 26285, "LastWriteTime": "2025-08-06T21:57:59.3913133+00:00"}, "nXQxY7a4LHNQf9yVOIf4veJDN430YTRnrcFOoFZcqIk=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\wdlrvgww7e-k72fsduyas.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=k72fsduyas}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8y9r0oetv", "Integrity": "ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64493, "LastWriteTime": "2025-08-06T21:57:59.3843136+00:00"}, "XdyzhoEc3WOIC0hsGXg10nMspH26eriAIPXQ8TE5xs0=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\x92en9a9mp-cwuvm2sdc3.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=cwuvm2sdc3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3uebi1j6we", "Integrity": "28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 38707, "LastWriteTime": "2025-08-06T21:57:59.4013116+00:00"}, "lk3dlUZCtoJBgWevT1xocYD3NQl96L5yShGApnk4dQw=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\o03tar9ax1-eve9uzuztn.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=eve9uzuztn}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "43czolro0z", "Integrity": "EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56387, "LastWriteTime": "2025-08-06T21:57:59.3893124+00:00"}, "YwgwVMC1VKxcjDqeYlm0MoFKrF1d8JCj8HxpvuRtseU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\k82aaqjmgf-n5tfi6zt97.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=n5tfi6zt97}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fd8at7e5rf", "Integrity": "Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 28200, "LastWriteTime": "2025-08-06T21:57:59.3708978+00:00"}, "FcGLGVkv9fbxmYuRcWMntsMxZILXJ08XejMLddo/NtI=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\oqgoo69acv-wf6sfai52w.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=wf6sfai52w}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktgldfzmo5", "Integrity": "aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64236, "LastWriteTime": "2025-08-06T21:57:59.4103133+00:00"}, "6wkC5UTVi4F0Zi6y3RSD0cQoW3BWHa5GFTyJHL3Yvqg=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\b2gqtoi2v8-ja11lcg8ur.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ja11lcg8ur}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8nhkk7sb8", "Integrity": "HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 38013, "LastWriteTime": "2025-08-06T21:57:59.3923144+00:00"}, "4cLpYeyyosmGYEvc0zE1U84fXY5Kh4kZLuAdtBWfjZw=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\dkozbjx6t1-okq9zf051y.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=okq9zf051y}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zm0g72duv", "Integrity": "T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86520, "LastWriteTime": "2025-08-06T21:57:59.3833144+00:00"}, "Oj7zKzaj4kfgdvKKB4KRIEPQMdw/lsySgmG81FYb6Ew=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\sw7twzm0gz-252a5wndhh.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=252a5wndhh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktya8jz5ov", "Integrity": "yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 32944, "LastWriteTime": "2025-08-06T21:57:59.4143125+00:00"}, "65r6AasJgwCtP/dIK9eRhY+cGkmKBizy+h7S5bn6ha8=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\iqna1yvk48-fxquxrv84i.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=fxquxrv84i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xhofi<PERSON><PERSON>", "Integrity": "xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92278, "LastWriteTime": "2025-08-06T21:57:59.4093133+00:00"}, "iRqdraum3P1gs93sCPl1It6da8jjUJSzbbKTXf81Fek=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\alht8ht9n7-iy2auvubsp.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=iy2auvubsp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42jbnex9ve", "Integrity": "PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 54528, "LastWriteTime": "2025-08-06T21:57:59.3893124+00:00"}, "inL5c/lQhzI2MuKA3ptneJHQR4wEmxxxX4PiWb4aDC4=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\dir3evalzp-ft3s53vfgj.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-08-06T21:57:59.4073107+00:00"}, "UEOPDNkm3/N8WKu44bA+GCqEUdppByZLfP2Z+bbk9nM=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\3td72ajpnc-c63t5i9ira.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a66bztmsz2", "Integrity": "AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31142, "LastWriteTime": "2025-08-06T21:57:59.3913133+00:00"}, "jgDrt4DhAMvMd1BsGmC9JTuTsUUUOXrtxn1377biGCM=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\h627czx73f-hrwsygsryq.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-08-06T21:57:59.3943142+00:00"}, "0cADz9Nz3lPcCoaWPiBMCkHkVDM/wWjeyweYrv8cdXg=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\iunmkf7vrb-ynyaa8k90p.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ws8e397h8g", "Integrity": "EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33667, "LastWriteTime": "2025-08-06T21:57:59.4103133+00:00"}, "m6+znx0rw70mN770BpXbR995P0URLfIEgxNquX+1fCU=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\dpx8dg3hki-v0zj4ognzu.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-08-06T21:57:59.4003117+00:00"}, "jL2GyqrRxICb3LRbPkC+2W6uc/DKtztX61m2oFOorpU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\kh52fnj1t5-43atpzeawx.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny7oqyylde", "Integrity": "sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31123, "LastWriteTime": "2025-08-06T21:57:59.3748951+00:00"}, "X5U6HfFBZu+1ZDvgk7FrBiS2FkDDwTd9ZZB8eOQhbv4=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\wwxjh0tqb5-pj5nd1wqec.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-08-06T21:57:59.4193143+00:00"}, "u5UF9RxMVtB9vEibnBmxMty0nDqj0rVeDYxSIYTuXeE=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\1bxgs52le7-zub09dkrxp.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ndo96zskmb", "Integrity": "q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33770, "LastWriteTime": "2025-08-06T21:57:59.3953158+00:00"}, "gaEDAvnwREYmEOqTVuvOjk9mHRDPLrmCqJPrJ2hOTtQ=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\8qil2ml6xf-nvvlpmu67g.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-08-06T21:57:59.3833144+00:00"}, "2pa+3UWfVNx8z8GVf14GdyCgXE/y+M0+SGTWASQ5Po0=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\356jgs3b6q-keugtjm085.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b271r4kg0j", "Integrity": "A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11132, "LastWriteTime": "2025-08-06T21:57:59.3973144+00:00"}, "xTmQ3j04WnNtgadiPbFw4ceqjMvXPw24K3jAaQJbkDY=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\mdrj46vdyz-j5mq2jizvt.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-08-06T21:57:59.3893124+00:00"}, "Me+m2RNz/cZK4ds3uWb53qQTXDX/29bXTViSPf4xrFA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\k2ikjqw9ce-d4r6k3f320.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9e8v2fr2b", "Integrity": "0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12194, "LastWriteTime": "2025-08-06T21:57:59.3738963+00:00"}, "NJGac0t9S1T5oFO58BPzfqiyubTzzdNan5Yuy/+NHsQ=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\8acc8zdz5p-c2oey78nd0.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-08-06T21:57:59.3933128+00:00"}, "uHqT4V3WpahCjcTgf7Q2+rsDLvfhSCquKYKknJCeXpM=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\j9m9vubjhm-wl58j5mj3v.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dzz7zk1je", "Integrity": "Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11153, "LastWriteTime": "2025-08-06T21:57:59.3873135+00:00"}, "BACKXPwO74H0mo34HKME0kr3kPR+yuhXdNSoE/s4YiA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\zaq01k6aj8-r4e9w2rdcm.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-08-06T21:57:59.3778976+00:00"}, "5hBq0JXD94XidzSvfMSPSXFtLT7yhVTtRjUg8OOpDFQ=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\jvfnk970r6-gye83jo8yx.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3pw7iimyz", "Integrity": "DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12244, "LastWriteTime": "2025-08-06T21:57:59.3983155+00:00"}, "w4OMw1vEiSrXdkJZ8c0J4BWQzrrLM2ldST9yZEDPmwI=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\v0yt5xmjoa-jd9uben2k1.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-08-06T21:57:59.3923144+00:00"}, "n7Qz6/IJCXusiFcyn/GlTsBECxW3AyhX1zDCuyxuuXs=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\41hwvam6ld-q9ht133ko3.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d645a0veuj", "Integrity": "ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3235, "LastWriteTime": "2025-08-06T21:57:59.375896+00:00"}, "7oRjm54NJ1J5Qyac692YL8+scq3HPGQL22/QK3Z/7kc=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\k723h2movt-ee0r1s7dh0.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-08-06T21:57:59.3863134+00:00"}, "du6fF75oUTakrqGrxHRwR1GdhoGlFTQYOBMuWn5L6Yg=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\eovc2x63tg-rxsg74s51o.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjx614p1f2", "Integrity": "tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3395, "LastWriteTime": "2025-08-06T21:57:59.3738963+00:00"}, "5TpOS6wXczfjpryR+V/SurHWS2YgBp+2qZQIhJ49HiE=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\jpxe5yqdj9-fsbi9cje9m.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-08-06T21:57:59.3678962+00:00"}, "5QVD6QbEv6u6FsRW5Au6nsWXFQE1nxddcq8EsMNXKxc=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\7ixhcdsvi5-tmc1g35s3z.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wl3mrbh96", "Integrity": "mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3204, "LastWriteTime": "2025-08-06T21:57:59.3943142+00:00"}, "xIEAKK5HEMzZZMaNmPhbyRZPdMc95gQBH0k1q9sXErY=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\n8b8oxdoha-fvhpjtyr6v.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-08-06T21:57:59.3843136+00:00"}, "wtuDWjSl4JpXsS4y8JfT0tIcqYvxbU1h+yDz2jbO2ic=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\3mp2sejyk9-qesaa3a1fm.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhemft0x44", "Integrity": "GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3406, "LastWriteTime": "2025-08-06T21:57:59.3688963+00:00"}, "5KZqS0tM83TNkc4ZBg5zsyT9H7r5yurDWubVTlfBDBQ=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\s19q5lb0r9-cosvhxvwiu.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-08-06T21:57:59.3843136+00:00"}, "Le8hSHTsXmwR+C8TygLqULBd1JJMxqsTl1PSgF8HTLM=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\3tw20sx7mb-22vffe00uq.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ki2uzdiv5", "Integrity": "cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6108, "LastWriteTime": "2025-08-06T21:57:59.3748951+00:00"}, "JBHtWv56z/rG+dOpFin1CKb6+7uVbVj373H09KOyJKk=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\mk8s5dumf5-ausgxo2sd3.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-08-06T21:57:59.3718976+00:00"}, "RSzyBTuWGpb3KZEd+/WxZC+aRRgHDWSKoRFqiYU4KWM=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\0ii28lkdjo-xvp3kq03qx.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z7y6dfz9j3", "Integrity": "vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6975, "LastWriteTime": "2025-08-06T21:57:59.399313+00:00"}, "axIMHiySwc9Bd1JF6dzO2m2wWOqaGcZ84oCHNVUPpHI=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\wtjtnptcj1-aexeepp0ev.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-08-06T21:57:59.3768958+00:00"}, "RHZVN9E8WTsj9gcEalNAULdsye/sPF6vPZW1L24a8ts=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\xq3d8yg16u-sejl45xvog.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ru0tedr80l", "Integrity": "k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6106, "LastWriteTime": "2025-08-06T21:57:59.3668946+00:00"}, "WV34hgXlv+DoeZmwjdTVt/iR1lYilwrglKz77kWIBzg=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\66bpgrr88p-c2jlpeoesf.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-08-06T21:57:59.3903115+00:00"}, "P16OxsTm7pFxNCes3IaD2WEzAH6MW7EZlvUfLVydgVk=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\ma60horqfq-t1cqhe9u97.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4t5zwq0ztq", "Integrity": "KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6973, "LastWriteTime": "2025-08-06T21:57:59.3778976+00:00"}, "DvTCJpaqtuNfsIKgcyTzJBPcqhWjB77P94Y2LP7BWpE=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\17j0rgum0i-d3h8l9wove.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "app#[.{fingerprint=d3h8l9wove}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fcjvn85lhy", "Integrity": "UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\app.css", "FileLength": 1511, "LastWriteTime": "2025-08-06T21:57:59.3728957+00:00"}}, "CachedCopyCandidates": {}}