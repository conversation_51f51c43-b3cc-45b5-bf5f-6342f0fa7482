{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Miller.WMS.Core.CDCService/1.0.0": {"dependencies": {"Aspire.Elastic.Clients.Elasticsearch": "9.2.1-preview.1.25222.1", "Aspire.Npgsql.EntityFrameworkCore.PostgreSQL": "9.4.0", "Microsoft.Extensions.Hosting": "9.0.8", "Miller.WMS.Data": "1.0.0", "Miller.WMS.Domain": "1.0.0", "Miller.WMS.ServiceDefaults": "1.0.0", "Npgsql": "9.0.3"}, "runtime": {"Miller.WMS.Core.CDCService.dll": {}}}, "Aspire.Elastic.Clients.Elasticsearch/9.2.1-preview.1.25222.1": {"dependencies": {"Elastic.Clients.Elasticsearch": "8.17.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8", "OpenTelemetry.Extensions.Hosting": "1.12.0"}, "runtime": {"lib/net8.0/Aspire.Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.125.22201"}}}, "Aspire.Npgsql.EntityFrameworkCore.PostgreSQL/9.4.0": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8", "Npgsql.DependencyInjection": "9.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.OpenTelemetry": "9.0.3", "OpenTelemetry.Extensions.Hosting": "1.12.0"}, "runtime": {"lib/net9.0/Aspire.Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Elastic.Clients.Elasticsearch/8.17.3": {"dependencies": {"Elastic.Transport": "0.5.9"}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Elastic.Transport/0.5.9": {"runtime": {"lib/net8.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/9.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.8", "Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36802"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36802"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.8": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.8", "Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31607"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.7.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Caching.Memory/9.0.8": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.7.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.ObjectPool": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.7.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Diagnostics/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.7.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.7": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore/9.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.Features/8.0.18": {}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileSystemGlobbing": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Hosting/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.Configuration.CommandLine": "9.0.8", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.8", "Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Configuration": "9.0.8", "Microsoft.Extensions.Logging.Console": "9.0.8", "Microsoft.Extensions.Logging.Debug": "9.0.8", "Microsoft.Extensions.Logging.EventLog": "9.0.8", "Microsoft.Extensions.Logging.EventSource": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Http/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Http.Diagnostics/9.7.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.7.0", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8", "Microsoft.Extensions.Telemetry": "9.7.0", "System.IO.Pipelines": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Http.Resilience/9.7.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.Http.Diagnostics": "9.7.0", "Microsoft.Extensions.ObjectPool": "9.0.7", "Microsoft.Extensions.Resilience": "9.7.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Logging/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Console/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Configuration": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Debug/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "System.Diagnostics.EventLog": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.ObjectPool/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Resilience/9.7.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.8", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.7.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8", "Microsoft.Extensions.Telemetry.Abstractions": "9.7.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.ServiceDiscovery/9.4.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Features": "8.0.18", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8", "Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.4.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.4.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Features": "8.0.18", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Microsoft.Extensions.Telemetry/9.7.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.7.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.7.0", "Microsoft.Extensions.Logging.Configuration": "9.0.8", "Microsoft.Extensions.ObjectPool": "9.0.7", "Microsoft.Extensions.Telemetry.Abstractions": "9.7.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.7.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.7.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.ObjectPool": "9.0.7", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.700.25.35602"}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Npgsql.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.DependencyInjection.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.8", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "9.0.4.0", "fileVersion": "9.0.4.0"}}}, "Npgsql.OpenTelemetry/9.0.3": {"dependencies": {"Npgsql": "9.0.3", "OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net6.0/Npgsql.OpenTelemetry.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "OpenTelemetry/1.12.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Configuration": "9.0.8", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api/1.12.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"dependencies": {"OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"assemblyVersion": "1.12.0.490", "fileVersion": "1.12.0.490"}}}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"assemblyVersion": "1.12.0.493", "fileVersion": "1.12.0.493"}}}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"dependencies": {"OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"assemblyVersion": "1.12.0.496", "fileVersion": "1.12.0.496"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "System.Diagnostics.DiagnosticSource/9.0.0": {}, "System.Diagnostics.EventLog/9.0.8": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.IO.Pipelines/9.0.7": {}, "System.Threading.RateLimiting/8.0.0": {}, "Miller.WMS.Data/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.8", "Miller.WMS.Domain": "1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4"}, "runtime": {"Miller.WMS.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Miller.WMS.Domain/1.0.0": {"runtime": {"Miller.WMS.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Miller.WMS.ServiceDefaults/1.0.0": {"dependencies": {"Microsoft.Extensions.Http.Resilience": "9.7.0", "Microsoft.Extensions.ServiceDiscovery": "9.4.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0"}, "runtime": {"Miller.WMS.ServiceDefaults.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Miller.WMS.Core.CDCService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspire.Elastic.Clients.Elasticsearch/9.2.1-preview.1.25222.1": {"type": "package", "serviceable": true, "sha512": "sha512-5BNXyj887fQmJcsMFxwahhdJZnWxcDkAqUKa9hDjCvRVt0W+a7CUo/LQWukFGYaKEzqiwCpAbSM18wy2Nud2cA==", "path": "aspire.elastic.clients.elasticsearch/9.2.1-preview.1.25222.1", "hashPath": "aspire.elastic.clients.elasticsearch.9.2.1-preview.1.25222.1.nupkg.sha512"}, "Aspire.Npgsql.EntityFrameworkCore.PostgreSQL/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlvTWkg/2STOF2PvNLvyubeiL332BS/rE9RPY9uEYePdRERB767sor7tVwd8ed5Y4dTfFNXIRVeE2zi0nlM2XA==", "path": "aspire.npgsql.entityframeworkcore.postgresql/9.4.0", "hashPath": "aspire.npgsql.entityframeworkcore.postgresql.9.4.0.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/8.17.3": {"type": "package", "serviceable": true, "sha512": "sha512-ucDLtAb6LI9ZqkS4yHkKifnDokAgNj4V3ZCahV9sMScDqDfxDeJeUMTBSm0yVExWszWsnCzhwp0oy5xM+BKx9w==", "path": "elastic.clients.elasticsearch/8.17.3", "hashPath": "elastic.clients.elasticsearch.8.17.3.nupkg.sha512"}, "Elastic.Transport/0.5.9": {"type": "package", "serviceable": true, "sha512": "sha512-RXoJAeXKaXq67SeLhyDMBPpvlkG940jqJWd5l+rKglMRLJn6X0dkLNlWehpDuouicNdwnYdcuADnqdSOiSvASw==", "path": "elastic.transport/0.5.9", "hashPath": "elastic.transport.0.5.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-bNGdPhN762+BIIO5MFYLjafRqkSS1MqLOc/erd55InvLnFxt9H3N5JNsuag1ZHyBor1VtD42U0CHpgqkWeAYgQ==", "path": "microsoft.entityframeworkcore/9.0.8", "hashPath": "microsoft.entityframeworkcore.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-B2yfAIQRRAQ4zvvWqh+HudD+juV3YoLlpXnrog3tU0PM9AFpuq6xo0+mEglN1P43WgdcUiF+65CWBcZe35s15Q==", "path": "microsoft.entityframeworkcore.abstractions/9.0.8", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2EYStCXt4Hi9p3J3EYMQbItJDtASJd064Kcs8C8hj8Jt5srILrR9qlaL0Ryvk8NrWQoCQvIELsmiuqLEZMLvGA==", "path": "microsoft.entityframeworkcore.analyzers/9.0.8", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-Yo5joquG7L79H5BhtpqP8apu+KFOAYfvmj0dZnVkPElBY14wY5qva0SOcrDWzYw5BrJrhIArfCcJCJHBvMYiKg==", "path": "microsoft.entityframeworkcore.relational/9.0.7", "hashPath": "microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-tgQEjZlMoDvy7xatRBYtVCWrnSFpQOqkfcxx4vwou0AWmjVvdNAqjXM/YYcM3Lcj8OP302xPpQ4X/aklqkfs+A==", "path": "microsoft.extensions.ambientmetadata.application/9.7.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4h7bsVoKoiK+SlPM+euX/ayGnKZhl47pPCidLTiio9xyG+vgVVfcYxcYQgjm0SCrdSxjG0EGIAKF8EFr3G8Ifw==", "path": "microsoft.extensions.caching.abstractions/9.0.8", "hashPath": "microsoft.extensions.caching.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-grR+oPyj8HVn4DT8CFUUdSw2pZZKS13KjytFe4txpHQliGM1GEDotohmjgvyl3hm7RFB3FRqvbouEX3/1ewp5A==", "path": "microsoft.extensions.caching.memory/9.0.8", "hashPath": "microsoft.extensions.caching.memory.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/oQydlBfhE83OwLXSdxoOqQaiquX+l24ptnSB8hFBw5o/BiY0Npg6DItB/87M+lBj9EAAVp3Kj9phlgVrr8P7Q==", "path": "microsoft.extensions.compliance.abstractions/9.7.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-0vK9DnYrYChdiH3yRZWkkp4x4LbrfkWEdBc5HOsQ8t/0CLOWKXKkkhOE8A1shlex0hGydbGrhObeypxz/QTm+w==", "path": "microsoft.extensions.configuration.binder/9.0.8", "hashPath": "microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vB6eDQ5prED5jHBqmSDNYzlCXsTSylYY7co9c7guhnz0zhx+jZ8BTHgO7y/Wl1dV2jAO15mKNWuyHRIRtWwGQg==", "path": "microsoft.extensions.configuration.commandline/9.0.8", "hashPath": "microsoft.extensions.configuration.commandline.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-9qileEYXDodlPN9DfPd5sHSfU2nSrI1r5BHVqLaLyb/7mPi335cy4ar/0ix4tXb2Aer/Pu4e5/zdwxt7lrtSyQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.8", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2jgx58Jpk3oKT7KRn8x/cFf3QDTjQP+KUbyBnynAcB2iBx1Eq9EdNMCu0QEbYuaZOaQru/Kwdffary+hn58Wwg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.8", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vjxzcnL7ul322+kpvELisXaZl8/5MYs6JfI9DZLQWsao1nA/4FL48yPwDK986hbJTWc64JxOOaMym0SQ/dy32w==", "path": "microsoft.extensions.configuration.json/9.0.8", "hashPath": "microsoft.extensions.configuration.json.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UgH18nQkuMJgxjn1539I83N6LhnKQlLhQm3ppe+PGsFpYsC6eGpF/1KvDRm/bmqsrg0NXhurrv4k2r0e8vWX/Q==", "path": "microsoft.extensions.configuration.usersecrets/9.0.8", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-bZ1wzULmBGxtfAaYSqJE4WM62i7JXI5sXkV1J6mZ01GIzRMbvEJc81kPehz8LOD7A90qRkVT6UWBHCBxZch5Rw==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.7.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-BKkLCFXzJvNmdngeYBf72VXoZqTJSb1orvjdzDLaGobicoGFBPW8ug2ru1nnEewMEwJzMgnsjHQY8EaKWmVhKg==", "path": "microsoft.extensions.diagnostics/9.0.8", "hashPath": "microsoft.extensions.diagnostics.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UDY7blv4DCyIJ/8CkNrQKLaAZFypXQavRZ2DWf/2zi1mxYYKKw2t8AOCBWxNntyPZHPGhtEmL3snFM98ADZqTw==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.8", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-wRdQuf/818X9ujg59fCaxMq2NGOqf8eFZJ4tBdGvwkLgv9vCwK+3blX/aNe7Ds4GHy+buB+HNMtSRv8xe9890Q==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.7.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nWutKqF6Eo0x8JE7Zo6eWtjjGynqjHvEQgpRTzavkU9VKwFECcZFBghpkEUCoDjeMCVafzGQTlQw7DsWHNnlsQ==", "path": "microsoft.extensions.diagnostics.healthchecks/9.0.7", "hashPath": "microsoft.extensions.diagnostics.healthchecks.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-kDvMq6n3hzQw2mvFwmj/z4OGlTPCg0fGjbM8C+4qS+SxYv4+QTFOY7nZNGfY0CGb40oPKlZQZD9TVQjMSj2atw==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.7", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sxg7sAdPxJtiYexLc7Ltv6B4fIfPPiMeGYYK1VGVd2r8LlDGAC6+e6eDwMg/p/3kBXwHZRMf/aBcCLwYQhQKNw==", "path": "microsoft.extensions.diagnostics.healthchecks.entityframeworkcore/9.0.7", "hashPath": "microsoft.extensions.diagnostics.healthchecks.entityframeworkcore.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-TflSnyz8Cc16IWNif+/sbZ5GsCcoD2od8oe6tajW75DbUtYGOsDv3tNRBV+8nRsOz/ETBuMPFAgXbllNdHFQxQ==", "path": "microsoft.extensions.features/8.0.18", "hashPath": "microsoft.extensions.features.8.0.18.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4zZbQ4w+hCMm9J+z5NOj3giIPT2MhZxx05HX/MGuAmDBbjOuXlYIIRN+t4V6OLxy5nXZIcXO+dQMB/OWubuDkw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-FlOe2i7UUIfY0l0ChaIYtlXjdWWutR4DMRKZaGD6z4G1uVTteFkbBfxUIoi1uGmrZQxXe/yv/cfwiT0tK2xyXA==", "path": "microsoft.extensions.fileproviders.physical/9.0.8", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-96Ub5LmwYfIGVoXkbe4kjs+ivK6fLBTwKJAOMfUNV0R+AkZRItlgROFqXEWMUlXBTPM1/kKu26Ueu5As6RDzJA==", "path": "microsoft.extensions.filesystemglobbing/9.0.8", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-O2VlzORrBbS2it203k5FOHrudDdmdrJovA73P/shdRGeLzvet4e4yXhGx52V2PNjYBQ0IO5M4xiNcL+6xIX6Bg==", "path": "microsoft.extensions.hosting/9.0.8", "hashPath": "microsoft.extensions.hosting.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-W<PERSON>rad20tySNCPe9aJUK7Wfwh+RiyLF+id02FKW8Qfc+HAzNQHazcqMXAbwG/kmbS89uvan/nKK1MufkRahjrJA==", "path": "microsoft.extensions.hosting.abstractions/9.0.8", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-KV2DyFzTyZlrmKBF7IHrg+OhdetkeeByC35vVp50CZogNCbO6c4nzBcjJNnGU0S+CMcrvsN2s8OI5lHwL0wv8A==", "path": "microsoft.extensions.http/9.0.7", "hashPath": "microsoft.extensions.http.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-MBF4UMnGdGTXnWezPQqHbIEnVt3sTj/r394C+zI3ixjjbhKoe/OuiJJcYwKNGDyJPBozSYFM6kS58vvojSHv6g==", "path": "microsoft.extensions.http.diagnostics/9.7.0", "hashPath": "microsoft.extensions.http.diagnostics.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-sZbOesepWGxtVJGa4lf5W0640ZHKjx3SZMatPooZFXkgLFpcECCi1kdau3HFQDcKqdka9ynfXUHdfVp3xx2i1A==", "path": "microsoft.extensions.http.resilience/9.7.0", "hashPath": "microsoft.extensions.http.resilience.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Z/7ze+0iheT7FJeZPqJKARYvyC2bmwu3whbm/48BJjdlGVvgDguoCqJIkI/67NkroTYobd5geai1WheNQvWrgA==", "path": "microsoft.extensions.logging/9.0.8", "hashPath": "microsoft.extensions.logging.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "path": "microsoft.extensions.logging.abstractions/9.0.8", "hashPath": "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Us4evDN3lbp1beVgrpxkSXKrbntVGAK+YbSo9P9driiU9PK05+ShhgesJ3aj7SuDfr3mqqcEgrMJ87Vu8t5dhw==", "path": "microsoft.extensions.logging.configuration/9.0.8", "hashPath": "microsoft.extensions.logging.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mPp9xB9MjiPuodh9z/+6zEGNj2kSVeXQtdbIBHlhUYqxX22gzJkx0ycPY42q4/OT/SzFV/TJ989Pa3sA/8ZBeA==", "path": "microsoft.extensions.logging.console/9.0.8", "hashPath": "microsoft.extensions.logging.console.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OwHQFVITsONEoizShc1yNYTUvMq0kT9j/LhwAKMsA7OZqtrBXuqjosbSvzkJZ9o+KWAozDh5Y1Vtpe5p/8/1qA==", "path": "microsoft.extensions.logging.debug/9.0.8", "hashPath": "microsoft.extensions.logging.debug.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-/gMwlll21UJcaXlitUqd+rs9jH36EJz5BpFVPshyOqz5u0qyV1pFnTWm5vhyx+g6gwVYENSLgpazR1urNv83xw==", "path": "microsoft.extensions.logging.eventlog/9.0.8", "hashPath": "microsoft.extensions.logging.eventlog.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-aGMFc/1P+315d07iyxSe6lEoZ0JjOPJ+Mfv9rrV2PvR2DFu1/pSi/SItHw1iChJOZgslNKJE97g1a9nLX3qQYA==", "path": "microsoft.extensions.logging.eventsource/9.0.8", "hashPath": "microsoft.extensions.logging.eventsource.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-9S4vPGg0NNBAxAkAGiOyWMAgDCmOK8uDnFryhcahmOqyArrI0MXju60Yk+UpDwXafVmjj+U0kJXwEyXjSJ3icA==", "path": "microsoft.extensions.objectpool/9.0.7", "hashPath": "microsoft.extensions.objectpool.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-eW2s6n06x0w6w4nsX+SvpgsFYkl+Y0CttYAt6DKUXeqprX+hzNqjSfOh637fwNJBg7wRBrOIRHe49gKiTgJxzQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.8", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mcmZRrM5zcFR04VxpVdf2Mwd8P6zN1EiUHGQQsNIC58URGxpv8Aj3VLKIAKv6WEZL+qpOZeyujxVHG4Qk9w8pQ==", "path": "microsoft.extensions.resilience/9.7.0", "hashPath": "microsoft.extensions.resilience.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-hUo/E5t1v9Pq6r1Eq1cPfDoE0TAPuCKtGr8JNOJhbzysuAl7Tun8K+d4pKe8ecYUCUKazIriYJTTWfsR73a4kg==", "path": "microsoft.extensions.servicediscovery/9.4.0", "hashPath": "microsoft.extensions.servicediscovery.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wy4igSehT66zFhwClMGsvSdbks/anJZPgE6k/V+w1rKe5gBmvlMyHHO0vCcjn1J6+LrEMxG1eBvNOyRHujc8hQ==", "path": "microsoft.extensions.servicediscovery.abstractions/9.4.0", "hashPath": "microsoft.extensions.servicediscovery.abstractions.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-U5EdfHzbbqVQv1EznG54VheBY6LnU/qpyasossnOu4hSNq3yXSUJHxE6u6PLw0S8DkMPrCjWvIvdPQNumpStxw==", "path": "microsoft.extensions.telemetry/9.7.0", "hashPath": "microsoft.extensions.telemetry.9.7.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-PgzFKLbv4ciRuRNJddHPp4UZ4Yh12WOSX2aNsVpNlSp9EsZKsSjatcx90x+xAJo8jChiPxTIkLnKWjl5fC5Lig==", "path": "microsoft.extensions.telemetry.abstractions/9.7.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.7.0.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.DependencyInjection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-McQ/xmBW9txjNzPyVKdmyx5bNVKDyc6ryz+cBOnLKxFH8zg9XAKMFvyNNmhzNjJbzLq8Rx+FFq/CeHjVT3s35w==", "path": "npgsql.dependencyinjection/9.0.3", "hashPath": "npgsql.dependencyinjection.9.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512"}, "Npgsql.OpenTelemetry/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-YVxm5ekM6UNbLto3ttNShOy13jffA0szhlEWSDMka7PzN+Srp2XaHmGbUb/2ulbtkXSk4CZruWwbjZxbMvw93Q==", "path": "npgsql.opentelemetry/9.0.3", "hashPath": "npgsql.opentelemetry.9.0.3.nupkg.sha512"}, "OpenTelemetry/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-aIEu2O3xFOdwIVH0AJsIHPIMH1YuX18nzu7BHyaDNQ6NWSk4Zyrs9Pp6y8SATuSbvdtmvue4mj/QZ3838srbwA==", "path": "opentelemetry/1.12.0", "hashPath": "opentelemetry.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xt0qldi+iE2szGrM3jAqzEMEJd48YBtqI6mge0+ArXTZg3aTpRmyhL6CKKl3bLioaFSSVbBpEbPin8u6Z46Yrw==", "path": "opentelemetry.api/1.12.0", "hashPath": "opentelemetry.api.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-t6Vk1143BfiisCWYbRcyzkAuN6Aq5RkYtfOSMoqCIRMvtN9p1e1xzc0nWQ+fccNGOVgHn3aMK5xFn2+iWMcr8A==", "path": "opentelemetry.api.providerbuilderextensions/1.12.0", "hashPath": "opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-7LzQSPhz5pNaL4xZgT3wkZODA1NLrEq3bet8KDHgtaJ9q+VNP7wmiZky8gQfMkB4FXuI/pevT8ZurL4p5997WA==", "path": "opentelemetry.exporter.opentelemetryprotocol/1.12.0", "hashPath": "opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-6/8O6rsJRwslg5/Fm3bscBelw4Yh9T9CN24p7cAsuEFkrmmeSO9gkYUCK02Qi+CmPM2KHYTLjKi0lJaCsDMWQA==", "path": "opentelemetry.extensions.hosting/1.12.0", "hashPath": "opentelemetry.extensions.hosting.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-r+Mzggd2P4N0Y34QIO6kakVPBOKFYSHnLkTrXXM+r37ABp+iaUvVUe+u/uxszsi5f7P5mrG0uYYaJ1QGHvzo3A==", "path": "opentelemetry.instrumentation.aspnetcore/1.12.0", "hashPath": "opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-0rW+MbHgUQAdbvBtRxPYoQBosbNdWegL7cYkRlxq+KQ/VFyU8itt4pWTccmu1/FWmTgqJyT3LaujyDZoRrm8Yg==", "path": "opentelemetry.instrumentation.http/1.12.0", "hashPath": "opentelemetry.instrumentation.http.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xmd0TAm2x+T3ztdf5BolIwLPh+Uy6osaBeIQtCXv611PN7h/Pnhsjg5lU2hkAWj7M7ns74U5wtVpS8DXmJ+94w==", "path": "opentelemetry.instrumentation.runtime/1.12.0", "hashPath": "opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-gebRF3JLLJ76jz1CQpvwezNapZUjFq20JQsaGHzBH0DzlkHBLpdhwkOei9usiOkIGMwU/L0ALWpNe1JE+5/itw==", "path": "system.diagnostics.eventlog/9.0.8", "hashPath": "system.diagnostics.eventlog.9.0.8.nupkg.sha512"}, "System.IO.Pipelines/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-I9KHYFNKQkufs/Ec7evpPPSu2HkuW+jNpq1kT0WOWjzuN6BjxRYy7CuWNLjQmuBzcKd9vKrHaPGcHVxSF5DadQ==", "path": "system.io.pipelines/9.0.7", "hashPath": "system.io.pipelines.9.0.7.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "Miller.WMS.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Miller.WMS.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Miller.WMS.ServiceDefaults/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}