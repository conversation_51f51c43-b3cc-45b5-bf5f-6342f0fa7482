//using Elastic.Clients.Elasticsearch;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.DependencyInjection;
//using Miller.WMS.Domain;
//using Miller.WMS.Shared.Data;

//namespace Miller.WMS.Edge.Tests;


//[Collection("AspireTestCollection")]
//public class CDCTests
//{
//    private readonly AspireTestFixture _fixture;

//    public CDCTests(AspireTestFixture fixture)
//    {
//        _fixture = fixture;
//    }

//    [Fact]
//    public async Task CDCServiceStartsSuccessfully()
//    {
//        // Arrange
//        var cancellationToken = TestContext.Current.CancellationToken;

//        // Act - Wait for the CDC service to start and become healthy
//        await _fixture.WaitForResourceHealthyAsync("wms-core-cdcservice", cancellationToken);

//        // Assert - If we reach this point, the CDC service started successfully
//        Assert.True(true, "CDC service started successfully");
//    }

//    [Fact]
//    public async Task ElasticsearchServiceIsHealthy()
//    {
//        // Arrange
//        var cancellationToken = TestContext.Current.CancellationToken;

//        // Act - Wait for Elasticsearch to be healthy
//        await _fixture.WaitForResourceHealthyAsync("wms-core-search", cancellationToken);

//        // Assert - If we reach this point, Elasticsearch is healthy
//        Assert.True(true, "Elasticsearch service is healthy");
//    }

//    [Fact]
//    public async Task InitialSyncCompletedSuccessfully()
//    {
//        // Arrange
//        var cancellationToken = TestContext.Current.CancellationToken;

//        // Wait for all required services
//        await _fixture.WaitForResourceHealthyAsync("wms-core-dataservice", cancellationToken);
//        await _fixture.WaitForResourceHealthyAsync("wms-core-search", cancellationToken);
//        await _fixture.WaitForResourceHealthyAsync("wms-core-cdcservice", cancellationToken);

//        // Give the CDC service time to complete initial sync
//        await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);

//        // Act & Assert - Create database context and Elasticsearch client
//        using var dbContext = CreateDbContext();
//        var elasticsearchClient = CreateElasticsearchClient();

//        // Get counts from both sources
//        var dbCount = await dbContext.Organizations.CountAsync(cancellationToken);
//        var esCountResponse = await elasticsearchClient.CountAsync<object>(c => c
//            .Indices("organizations"), cancellationToken);

//        // Assert
//        Assert.True(dbCount > 0, "Database should have organizations");
//        Assert.True(esCountResponse.IsValidResponse, "Elasticsearch count query should succeed");
//        Assert.Equal(dbCount, (int)esCountResponse.Count);
//    }

//    [Fact]
//    public async Task OrganizationDataSyncedCorrectly()
//    {
//        // Arrange
//        var cancellationToken = TestContext.Current.CancellationToken;

//        // Wait for all services and initial sync
//        await _fixture.WaitForResourceHealthyAsync("wms-core-dataservice", cancellationToken);
//        await _fixture.WaitForResourceHealthyAsync("wms-core-search", cancellationToken);
//        await _fixture.WaitForResourceHealthyAsync("wms-core-cdcservice", cancellationToken);

//        // Give time for initial sync
//        await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);

//        // Act
//        using var dbContext = CreateDbContext();
//        var elasticsearchClient = CreateElasticsearchClient();

//        // Get a sample organization from database
//        var dbOrganization = await dbContext.Organizations
//            .Include(o => o.Facilities)
//            .Include(o => o.Users)
//            .FirstAsync(cancellationToken);

//        // Get the same organization from Elasticsearch
//        var esResponse = await elasticsearchClient.GetAsync<dynamic>("organizations", dbOrganization.Id.ToString(), cancellationToken);

//        // Assert
//        Assert.True(esResponse.IsValidResponse, "Should be able to retrieve organization from Elasticsearch");
//        Assert.NotNull(esResponse.Source);
        
//        // Verify basic data matches
//        var esDoc = esResponse.Source as IDictionary<string, object>;
//        Assert.NotNull(esDoc);
//        Assert.Equal(dbOrganization.Id, Convert.ToInt32(esDoc["id"]));
//        Assert.Equal(dbOrganization.Name, esDoc["name"].ToString());
//    }

//    [Fact]
//    public async Task DatabaseChangesAreSyncedToElasticsearch()
//    {
//        // Arrange
//        var cancellationToken = TestContext.Current.CancellationToken;

//        // Wait for all services
//        await _fixture.WaitForResourceHealthyAsync("wms-core-dataservice", cancellationToken);
//        await _fixture.WaitForResourceHealthyAsync("wms-core-search", cancellationToken);
//        await _fixture.WaitForResourceHealthyAsync("wms-core-cdcservice", cancellationToken);

//        // Give time for initial sync
//        await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);

//        using var dbContext = CreateDbContext();
//        var elasticsearchClient = CreateElasticsearchClient();

//        // Get initial counts
//        var initialDbCount = await dbContext.Organizations.CountAsync(cancellationToken);
//        var initialEsCountResponse = await elasticsearchClient.CountAsync<object>(c => c
//            .Indices("organizations"), cancellationToken);
//        var initialEsCount = (int)initialEsCountResponse.Count;

//        // Act - Add a new organization to the database
//        var newOrganization = new Organization
//        {
//            Name = $"Test Organization {DateTime.UtcNow:yyyyMMddHHmmss}"
//        };

//        dbContext.Organizations.Add(newOrganization);
//        await dbContext.SaveChangesAsync(cancellationToken);

//        // Wait for CDC to detect and sync the change (polling interval is 5 seconds)
//        await Task.Delay(TimeSpan.FromSeconds(15), cancellationToken);

//        // Assert - Check that both database and Elasticsearch have the new count
//        var newDbCount = await dbContext.Organizations.CountAsync(cancellationToken);
//        var newEsCountResponse = await elasticsearchClient.CountAsync<object>(c => c
//            .Indices("organizations"), cancellationToken);
//        var newEsCount = (int)newEsCountResponse.Count;

//        Assert.Equal(initialDbCount + 1, newDbCount);
//        Assert.Equal(initialEsCount + 1, newEsCount);
//        Assert.Equal(newDbCount, newEsCount);
//    }

//    private WmsContext CreateDbContext()
//    {
//        var connectionString = _fixture.App!.Services.GetRequiredService<IConfiguration>().GetConnectionString("wms");
//        var options = new DbContextOptionsBuilder<WmsContext>()
//            .UseNpgsql(connectionString)
//            .Options;
//        return new WmsContext(options);
//    }

//    private ElasticsearchClient CreateElasticsearchClient()
//    {
//        // Get Elasticsearch connection from the app configuration
//        var configuration = _fixture.App!.Services.GetRequiredService<IConfiguration>();
//        var elasticsearchUrl = configuration.GetConnectionString("wms-core-search") ?? "http://localhost:9200";
        
//        var settings = new ElasticsearchClientSettings(new Uri(elasticsearchUrl));
//        return new ElasticsearchClient(settings);
//    }
//}
