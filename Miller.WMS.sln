Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36203.30
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Edge.AppHost", "Miller.WMS.Edge.AppHost\Miller.WMS.Edge.AppHost.csproj", "{9E5271F8-A733-45C9-B134-584133A30969}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.ServiceDefaults", "Miller.WMS.ServiceDefaults\Miller.WMS.ServiceDefaults.csproj", "{C393FAD7-28E0-0789-CE0F-705085E01431}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Edge.ApiService", "Miller.WMS.Edge.ApiService\Miller.WMS.Edge.ApiService.csproj", "{FA035236-ED7E-CEAE-E7DA-ABC865C38663}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Edge.Web", "Miller.WMS.Edge.Web\Miller.WMS.Edge.Web.csproj", "{AE4E386F-1F79-EEEC-33BF-B2017778B332}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Edge.Tests", "Miller.WMS.Edge.Tests\Miller.WMS.Edge.Tests.csproj", "{20B93718-07FD-1108-FCAD-13CE8456DD16}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Core.CDCService", "Miller.WMS.Core.CDCService\Miller.WMS.Core.CDCService.csproj", "{DDAFB011-116C-6929-0C37-929035D881D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Core.DataService", "Miller.WMS.Core.DataService\Miller.WMS.Core.DataService.csproj", "{FFC89C97-E406-86FD-4913-024DF28206AE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Data", "Miller.WMS.Data\Miller.WMS.Data.csproj", "{29263558-07B1-458E-B050-02D0765FA12A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Domain", "Miller.WMS.Domain\Miller.WMS.Domain.csproj", "{CCEB3DA7-D10D-4CFB-A3B2-BB1E80C8BA38}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9E5271F8-A733-45C9-B134-584133A30969}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E5271F8-A733-45C9-B134-584133A30969}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E5271F8-A733-45C9-B134-584133A30969}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E5271F8-A733-45C9-B134-584133A30969}.Release|Any CPU.Build.0 = Release|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Release|Any CPU.Build.0 = Release|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDAFB011-116C-6929-0C37-929035D881D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDAFB011-116C-6929-0C37-929035D881D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDAFB011-116C-6929-0C37-929035D881D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDAFB011-116C-6929-0C37-929035D881D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFC89C97-E406-86FD-4913-024DF28206AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFC89C97-E406-86FD-4913-024DF28206AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFC89C97-E406-86FD-4913-024DF28206AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFC89C97-E406-86FD-4913-024DF28206AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{29263558-07B1-458E-B050-02D0765FA12A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29263558-07B1-458E-B050-02D0765FA12A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29263558-07B1-458E-B050-02D0765FA12A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29263558-07B1-458E-B050-02D0765FA12A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CCEB3DA7-D10D-4CFB-A3B2-BB1E80C8BA38}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CCEB3DA7-D10D-4CFB-A3B2-BB1E80C8BA38}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CCEB3DA7-D10D-4CFB-A3B2-BB1E80C8BA38}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CCEB3DA7-D10D-4CFB-A3B2-BB1E80C8BA38}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {05840D3F-4D13-4EDF-9027-D6ACE772CE86}
	EndGlobalSection
EndGlobal
