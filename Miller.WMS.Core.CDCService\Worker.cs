using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Elastic.Clients.Elasticsearch;
using Miller.WMS.Core.CDCService.Models;
using Miller.WMS.Shared.Data;
using Npgsql;

namespace Miller.WMS.Core.CDCService;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IServiceProvider _serviceProvider;
    private bool _initialSyncCompleted = false;
    private const string IndexName = "organizations";

    public Worker(
        ILogger<Worker> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("CDC Worker starting up");

        try
        {
            // Perform initial sync with retries
            await PerformInitialSyncWithRetriesAsync(stoppingToken);

            // Start monitoring for changes
            await MonitorDatabaseChangesAsync(stoppingToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CDC Worker encountered an error");
            throw;
        }
    }

    private async Task PerformInitialSyncWithRetriesAsync(CancellationToken stoppingToken)
    {
        const int maxRetries = 5;
        const int delaySeconds = 10;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                _logger.LogInformation("Starting initial sync of organizations (attempt {Attempt}/{MaxRetries})", attempt, maxRetries);

                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<WmsContext>();
                var elasticsearchClient = scope.ServiceProvider.GetRequiredService<ElasticsearchClient>();

                await InitialSyncAsync(dbContext, elasticsearchClient, stoppingToken);

                var dbCount = await GetDatabaseOrganizationCountAsync(dbContext, stoppingToken);
                var esCount = await GetElasticsearchOrganizationCountAsync(elasticsearchClient, stoppingToken);

                _logger.LogInformation("Initial sync completed. Database: {DbCount}, Elasticsearch: {EsCount}", dbCount, esCount);
                _initialSyncCompleted = true;
                return; // Success, exit retry loop
            }
            catch (Exception ex) when (attempt < maxRetries)
            {
                _logger.LogWarning(ex, "Initial sync attempt {Attempt} failed, retrying in {DelaySeconds} seconds", attempt, delaySeconds);
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform initial sync after {MaxRetries} attempts", maxRetries);
                throw;
            }
        }
    }

    private async Task MonitorDatabaseChangesAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting database change monitoring");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Simple polling approach - check for changes every 5 seconds
                // In a production environment, you might want to use PostgreSQL's LISTEN/NOTIFY
                // or implement proper WAL monitoring, but this is a quick & dirty solution

                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

                if (!_initialSyncCompleted)
                    continue;

                // Check if database and Elasticsearch are in sync
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<WmsContext>();
                var elasticsearchClient = scope.ServiceProvider.GetRequiredService<ElasticsearchClient>();

                var dbCount = await GetDatabaseOrganizationCountAsync(dbContext, stoppingToken);
                var esCount = await GetElasticsearchOrganizationCountAsync(elasticsearchClient, stoppingToken);

                if (dbCount != esCount)
                {
                    _logger.LogInformation("Detected count mismatch. Database: {DbCount}, Elasticsearch: {EsCount}. Performing resync.", dbCount, esCount);
                    await InitialSyncAsync(dbContext, elasticsearchClient, stoppingToken);
                }
                else
                {
                    _logger.LogDebug("Database and Elasticsearch are in sync. Count: {Count}", dbCount);
                }
            }
            catch (Exception ex) when (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogError(ex, "Error during database change monitoring");
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken); // Wait before retrying
            }
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("CDC Worker stopping");
        await base.StopAsync(cancellationToken);
    }

    private async Task EnsureIndexExistsAsync(ElasticsearchClient elasticsearchClient, CancellationToken cancellationToken = default)
    {
        try
        {
            var existsResponse = await elasticsearchClient.Indices.ExistsAsync(IndexName, cancellationToken);

            if (!existsResponse.IsValidResponse)
            {
                _logger.LogError("Failed to check if Elasticsearch index exists: {Error}", existsResponse.DebugInformation);
                throw new InvalidOperationException($"Failed to check if Elasticsearch index exists: {existsResponse.DebugInformation}");
            }

            if (!existsResponse.Exists)
            {
                _logger.LogInformation("Creating Elasticsearch index: {IndexName}", IndexName);

                var createResponse = await elasticsearchClient.Indices.CreateAsync(IndexName, c => c
                    .Mappings(m => m
                        .Properties<OrganizationSearchDocument>(p => p
                            .IntegerNumber(n => n.Id)
                            .Text(t => t.Name, td => td.Analyzer("standard"))
                            .Date(d => d.LastUpdated)
                            .IntegerNumber(n => n.FacilityCount)
                            .IntegerNumber(n => n.UserCount)
                            .Text(t => t.FacilityNames, td => td.Analyzer("standard"))
                        )
                    ), cancellationToken);

                if (!createResponse.IsValidResponse)
                {
                    _logger.LogError("Failed to create Elasticsearch index: {Error}", createResponse.DebugInformation);
                    throw new InvalidOperationException($"Failed to create Elasticsearch index: {createResponse.DebugInformation}");
                }

                _logger.LogInformation("Successfully created Elasticsearch index: {IndexName}", IndexName);
            }
            else
            {
                _logger.LogInformation("Elasticsearch index {IndexName} already exists", IndexName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring Elasticsearch index exists");
            throw;
        }
    }

    private async Task InitialSyncAsync(WmsContext dbContext, ElasticsearchClient elasticsearchClient, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting initial sync of organizations to Elasticsearch");

        await EnsureIndexExistsAsync(elasticsearchClient, cancellationToken);

        // Get all organizations with their related data
        var organizations = await dbContext.Organizations
            .Include(o => o.Facilities)
            .Include(o => o.Users)
            .ToListAsync(cancellationToken);

        _logger.LogInformation("Found {Count} organizations to sync", organizations.Count);

        if (organizations.Count == 0)
        {
            _logger.LogInformation("No organizations found to sync");
            return;
        }

        // Convert to search documents
        var searchDocuments = organizations.Select(OrganizationSearchDocument.FromDomain).ToList();

        // Bulk index to Elasticsearch
        var bulkResponse = await elasticsearchClient.BulkAsync(b => b
            .Index(IndexName)
            .IndexMany(searchDocuments, (descriptor, doc) => descriptor.Id(doc.Id))
        , cancellationToken);

        if (!bulkResponse.IsValidResponse)
        {
            _logger.LogError("Failed to bulk index organizations: {Error}", bulkResponse.DebugInformation);
            throw new InvalidOperationException($"Failed to bulk index organizations: {bulkResponse.DebugInformation}");
        }

        _logger.LogInformation("Successfully synced {Count} organizations to Elasticsearch", organizations.Count);
    }

    private async Task<long> GetElasticsearchOrganizationCountAsync(ElasticsearchClient elasticsearchClient, CancellationToken cancellationToken = default)
    {
        var countResponse = await elasticsearchClient.CountAsync<OrganizationSearchDocument>(c => c
            .Indices(IndexName)
        , cancellationToken);

        if (!countResponse.IsValidResponse)
        {
            _logger.LogError("Failed to get Elasticsearch organization count: {Error}", countResponse.DebugInformation);
            return 0;
        }

        return countResponse.Count;
    }

    private async Task<int> GetDatabaseOrganizationCountAsync(WmsContext dbContext, CancellationToken cancellationToken = default)
    {
        return await dbContext.Organizations.CountAsync(cancellationToken);
    }
}
