using Microsoft.Extensions.DependencyInjection;
using Miller.WMS.Core.CDCService.Services;
using Npgsql;

namespace Miller.WMS.Core.CDCService;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IServiceProvider _serviceProvider;
    private bool _initialSyncCompleted = false;

    public Worker(
        ILogger<Worker> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("CDC Worker starting up");

        try
        {
            // Perform initial sync with retries
            await PerformInitialSyncWithRetriesAsync(stoppingToken);

            // Start monitoring for changes
            await MonitorDatabaseChangesAsync(stoppingToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CDC Worker encountered an error");
            throw;
        }
    }

    private async Task PerformInitialSyncWithRetriesAsync(CancellationToken stoppingToken)
    {
        const int maxRetries = 5;
        const int delaySeconds = 10;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                _logger.LogInformation("Starting initial sync of organizations (attempt {Attempt}/{MaxRetries})", attempt, maxRetries);

                using var scope = _serviceProvider.CreateScope();
                var syncService = scope.ServiceProvider.GetRequiredService<IOrganizationSyncService>();

                await syncService.InitialSyncAsync(stoppingToken);

                var dbCount = await syncService.GetDatabaseOrganizationCountAsync(stoppingToken);
                var esCount = await syncService.GetElasticsearchOrganizationCountAsync(stoppingToken);

                _logger.LogInformation("Initial sync completed. Database: {DbCount}, Elasticsearch: {EsCount}", dbCount, esCount);
                _initialSyncCompleted = true;
                return; // Success, exit retry loop
            }
            catch (Exception ex) when (attempt < maxRetries)
            {
                _logger.LogWarning(ex, "Initial sync attempt {Attempt} failed, retrying in {DelaySeconds} seconds", attempt, delaySeconds);
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform initial sync after {MaxRetries} attempts", maxRetries);
                throw;
            }
        }
    }

    private async Task PerformInitialSyncAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting initial sync of organizations");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var syncService = scope.ServiceProvider.GetRequiredService<IOrganizationSyncService>();

            await syncService.InitialSyncAsync(stoppingToken);

            var dbCount = await syncService.GetDatabaseOrganizationCountAsync(stoppingToken);
            var esCount = await syncService.GetElasticsearchOrganizationCountAsync(stoppingToken);

            _logger.LogInformation("Initial sync completed. Database: {DbCount}, Elasticsearch: {EsCount}", dbCount, esCount);
            _initialSyncCompleted = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform initial sync");
            throw;
        }
    }

    private async Task MonitorDatabaseChangesAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting database change monitoring");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Simple polling approach - check for changes every 5 seconds
                // In a production environment, you might want to use PostgreSQL's LISTEN/NOTIFY
                // or implement proper WAL monitoring, but this is a quick & dirty solution

                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

                if (!_initialSyncCompleted)
                    continue;

                // Check if database and Elasticsearch are in sync
                using var scope = _serviceProvider.CreateScope();
                var syncService = scope.ServiceProvider.GetRequiredService<IOrganizationSyncService>();

                var dbCount = await syncService.GetDatabaseOrganizationCountAsync(stoppingToken);
                var esCount = await syncService.GetElasticsearchOrganizationCountAsync(stoppingToken);

                if (dbCount != esCount)
                {
                    _logger.LogInformation("Detected count mismatch. Database: {DbCount}, Elasticsearch: {EsCount}. Performing resync.", dbCount, esCount);
                    await syncService.InitialSyncAsync(stoppingToken);
                }
                else
                {
                    _logger.LogDebug("Database and Elasticsearch are in sync. Count: {Count}", dbCount);
                }
            }
            catch (Exception ex) when (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogError(ex, "Error during database change monitoring");
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken); // Wait before retrying
            }
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("CDC Worker stopping");
        await base.StopAsync(cancellationToken);
    }
}
