using Elastic.Clients.Elasticsearch;
using Miller.WMS.Core.CDCService;
using Miller.WMS.Shared.Data;

var builder = Host.CreateApplicationBuilder(args);

builder.AddServiceDefaults();

// Add database context
builder.AddNpgsqlDbContext<WmsContext>("wms");

// Add Elasticsearch client using Aspire integration
builder.AddElasticsearchClient("wms-core-search");

// Add the worker service
builder.Services.AddHostedService<Worker>();

var host = builder.Build();
host.Run();
