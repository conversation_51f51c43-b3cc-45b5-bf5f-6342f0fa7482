# CDC Service Manual Testing Guide

## Prerequisites
1. Docker Desktop running
2. .NET 9 SDK installed
3. All project dependencies restored

## Manual Testing Steps

### 1. Start the Aspire Application
```bash
cd Miller.WMS.Edge.AppHost
dotnet run
```

This will start:
- PostgreSQL database with WAL enabled
- Elasticsearch for search indexing
- Data service (seeds initial data)
- CDC service (syncs data to Elasticsearch)
- API and Web services

### 2. Verify Services are Running
Open the Aspire dashboard (usually at http://localhost:15000) and verify:
- ✅ wms-core-psql (PostgreSQL) - Healthy
- ✅ wms-core-search (Elasticsearch) - Healthy  
- ✅ wms-core-dataservice - Completed (seeded data)
- ✅ wms-core-cdcservice - Running (syncing data)
- ✅ wms-edge-api - Healthy
- ✅ wms-edge-web - Healthy

### 3. Test Initial Sync
Check CDC service logs for:
```
CDC Worker starting up
Starting initial sync of organizations (attempt 1/5)
Successfully created Elasticsearch index: organizations
Successfully synced 10 organizations to Elasticsearch
Initial sync completed. Database: 10, Elasticsearch: 10
```

### 4. Verify Elasticsearch Index
Open Elasticsearch (usually at http://localhost:9200) and check:

```bash
# Check if index exists
curl http://localhost:9200/organizations

# Check document count
curl http://localhost:9200/organizations/_count

# Get sample documents
curl http://localhost:9200/organizations/_search?size=2
```

Expected results:
- Index exists with proper mapping
- Document count matches database (10 organizations)
- Documents contain organization data with facility/user counts

### 5. Test Change Detection
The CDC service polls every 5 seconds for changes. To test:

1. Connect to PostgreSQL database
2. Add a new organization:
```sql
INSERT INTO "Organizations" ("Name") VALUES ('Test CDC Organization');
```
3. Wait 5-15 seconds
4. Check CDC service logs for sync activity
5. Verify new organization appears in Elasticsearch

### 6. Test API Integration
```bash
# Test API health
curl http://localhost:5000/health

# Test ping endpoint
curl http://localhost:5000/ping
```

## Expected Behavior

### Production Mode
- CDC service runs continuously
- Monitors database changes every 5 seconds
- Syncs changes to Elasticsearch
- Handles failures with retry logic

### Test Mode
- CDC service performs initial sync
- Exits after sync completion (when EnvironmentName = "Testing")
- Allows test framework to complete

## Troubleshooting

### Common Issues
1. **Elasticsearch connection failed**: Ensure Docker is running and Elasticsearch container started
2. **Database connection failed**: Check PostgreSQL container status
3. **CDC service not starting**: Verify all dependencies are healthy first

### Log Locations
- CDC Service: Check Aspire dashboard logs for "wms-core-cdcservice"
- Database: Check "wms-core-psql" logs
- Elasticsearch: Check "wms-core-search" logs

## Success Criteria
✅ All services start successfully
✅ Initial sync completes without errors  
✅ Database count matches Elasticsearch count
✅ New database changes sync to Elasticsearch
✅ API endpoints respond correctly
✅ No error logs in CDC service
