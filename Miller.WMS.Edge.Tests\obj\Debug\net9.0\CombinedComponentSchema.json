{"properties": {"Azure": {"description": "Azure provisioning information. This section is used to create Azure resources for the app.", "type": "object", "properties": {"SubscriptionId": {"description": "Azure Subscription Id that will be used to host the resources.", "type": "string"}, "ResourceGroup": {"description": "Azure Resource Group name where to create resources.", "type": "string"}, "ResourceGroupPrefix": {"description": "Azure Resource Group prefix used in resource groups names created.", "type": "string"}, "AllowResourceGroupCreation": {"description": "Flag that shows if it is allowed to create a resource group if not exists. Defaults to false if ResourceGroup is provided.", "type": "boolean"}, "Location": {"description": "Azure Location that will be used to create resources.", "type": "string"}, "CredentialSource": {"description": "Configures which Azure credential to use for provisioning.", "enum": ["AzureCli", "AzurePowerShell", "VisualStudio", "VisualStudioCode", "AzureDeveloperCli", "InteractiveBrowser", "<PERSON><PERSON><PERSON>"], "default": "<PERSON><PERSON><PERSON>"}}}, "Aspire": {"type": "object", "properties": {"Npgsql": {"type": "object", "properties": {"EntityFrameworkCore": {"type": "object", "properties": {"PostgreSQL": {"type": "object", "properties": {"CommandTimeout": {"type": "integer", "description": "Gets or sets the time in seconds to wait for the command to execute."}, "ConnectionString": {"type": "string", "description": "Gets or sets the connection string of the PostgreSQL database to connect to."}, "DisableHealthChecks": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the database health check is disabled or not.", "default": false}, "DisableMetrics": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the OpenTelemetry metrics are disabled or not.", "default": false}, "DisableRetry": {"type": "boolean", "description": "Gets or sets whether retries should be disabled.", "default": false}, "DisableTracing": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the OpenTelemetry tracing is disabled or not.", "default": false}}, "description": "Provides the client configuration settings for connecting to a PostgreSQL database using EntityFrameworkCore."}}}}}, "Elastic": {"type": "object", "properties": {"Clients": {"type": "object", "properties": {"Elasticsearch": {"type": "object", "properties": {"ApiKey": {"type": "string", "description": "The API Key of the Elastic Cloud to connect to."}, "CloudId": {"type": "string", "description": "The CloudId of the Elastic Cloud to connect to."}, "DisableHealthChecks": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the Elasticsearch health check is disabled or not.", "default": false}, "DisableTracing": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the OpenTelemetry tracing is disabled or not.", "default": false}, "Endpoint": {"type": "string", "format": "uri", "description": "The endpoint URI string of the Elasticsearch to connect to."}, "HealthCheckTimeout": {"type": "integer", "description": "Gets or sets a integer value that indicates the Elasticsearch health check timeout in milliseconds."}}, "description": "Provides the client configuration settings for connecting to a Elasticsearch using Elastic.Clients.Elasticsearch."}}}}}}}}, "definitions": {"logLevel": {"properties": {"Microsoft.EntityFrameworkCore": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.ChangeTracking": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Database": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Database.Command": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Database.Connection": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Database.Transaction": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Infrastructure": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Migrations": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Model": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Model.Validation": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Query": {"$ref": "#/definitions/logLevelThreshold"}, "Microsoft.EntityFrameworkCore.Update": {"$ref": "#/definitions/logLevelThreshold"}}}}, "type": "object", "SourceSegments": "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure\\9.4.0\\AspireAzureConfigurationSchema.json;C:\\Users\\<USER>\\.nuget\\packages\\aspire.npgsql.entityframeworkcore.postgresql\\9.4.0\\ConfigurationSchema.json;C:\\Users\\<USER>\\.nuget\\packages\\aspire.elastic.clients.elasticsearch\\9.2.1-preview.1.25222.1\\ConfigurationSchema.json"}