var builder = DistributedApplication.CreateBuilder(args);

var username = builder.AddParameter("username", "guest", secret: false);
var password = builder.AddParameter("password", "guest", secret: false);

var corePsql = builder.AddPostgres("wms-core-psql", username, password)
    .WithLifetime(ContainerLifetime.Persistent)
    .WithArgs("-c", "wal_level=logical") // required for CDC
    .WithDbGate();

var corePsqlDb = corePsql.AddDatabase("wms");

var coreSearch = builder.AddElasticsearch("wms-core-search")
    .WithLifetime(ContainerLifetime.Persistent)
    ;

var dataService = builder.AddProject<Projects.Miller_WMS_Core_DataService>("wms-core-dataservice")
    .WithReference(corePsqlDb)
    .WaitFor(corePsqlDb);

builder.AddProject<Projects.Miller_WMS_Core_CDCService>("wms-core-cdcservice")
    .WithReference(corePsqlDb)
    .WithReference(coreSearch)
    .WaitFor(corePsqlDb)
    .WaitFor(coreSearch)
    //.WaitForCompletion(dataService)
    ;

var cache = builder.AddRedis("wms-edge-cache");

var apiService = builder.AddProject<Projects.Miller_WMS_Edge_ApiService>("wms-edge-api")
    .WithHttpHealthCheck("/health");

builder.AddProject<Projects.Miller_WMS_Edge_Web>("wms-edge-web")
    .WithExternalHttpEndpoints()
    .WithHttpHealthCheck("/health")
    .WithReference(cache)
    .WaitFor(cache)
    .WithReference(apiService)
    .WaitFor(apiService);

builder.Build().Run();
