﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.testing.platform\1.7.3\buildTransitive\net9.0\Microsoft.Testing.Platform.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.platform\1.7.3\buildTransitive\net9.0\Microsoft.Testing.Platform.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.7.3\buildTransitive\Microsoft.Testing.Platform.MSBuild.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.7.3\buildTransitive\Microsoft.Testing.Platform.MSBuild.targets')" />
    <Import Project="$(NuGetPackageRoot)xunit.v3.core\3.0.0\buildTransitive\xunit.v3.core.targets" Condition="Exists('$(NuGetPackageRoot)xunit.v3.core\3.0.0\buildTransitive\xunit.v3.core.targets')" />
    <Import Project="$(NuGetPackageRoot)system.text.json\6.0.11\buildTransitive\netcoreapp3.1\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\6.0.11\buildTransitive\netcoreapp3.1\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.8\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.8\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.8\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.8\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.8\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.8\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.7.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.7.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.7.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.7.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.8\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.8\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.azure.cosmos\3.52.0\buildTransitive\netstandard2.0\Microsoft.Azure.Cosmos.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.azure.cosmos\3.52.0\buildTransitive\netstandard2.0\Microsoft.Azure.Cosmos.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.hosting.orchestration.win-x64\9.4.0\buildTransitive\Aspire.Hosting.Orchestration.win-x64.targets" Condition="Exists('$(NuGetPackageRoot)aspire.hosting.orchestration.win-x64\9.4.0\buildTransitive\Aspire.Hosting.Orchestration.win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.hosting.azure\9.4.0\buildTransitive\Aspire.Hosting.Azure.targets" Condition="Exists('$(NuGetPackageRoot)aspire.hosting.azure\9.4.0\buildTransitive\Aspire.Hosting.Azure.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.dashboard.sdk.win-x64\9.4.0\buildTransitive\Aspire.Dashboard.Sdk.win-x64.targets" Condition="Exists('$(NuGetPackageRoot)aspire.dashboard.sdk.win-x64\9.4.0\buildTransitive\Aspire.Dashboard.Sdk.win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.npgsql.entityframeworkcore.postgresql\9.4.0\buildTransitive\net9.0\Aspire.Npgsql.EntityFrameworkCore.PostgreSQL.targets" Condition="Exists('$(NuGetPackageRoot)aspire.npgsql.entityframeworkcore.postgresql\9.4.0\buildTransitive\net9.0\Aspire.Npgsql.EntityFrameworkCore.PostgreSQL.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.elastic.clients.elasticsearch\9.2.1-preview.1.25222.1\buildTransitive\net8.0\Aspire.Elastic.Clients.Elasticsearch.targets" Condition="Exists('$(NuGetPackageRoot)aspire.elastic.clients.elasticsearch\9.2.1-preview.1.25222.1\buildTransitive\net8.0\Aspire.Elastic.Clients.Elasticsearch.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testplatform.testhost\17.14.1\build\net8.0\Microsoft.TestPlatform.TestHost.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.testplatform.testhost\17.14.1\build\net8.0\Microsoft.TestPlatform.TestHost.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codecoverage\17.14.1\build\netstandard2.0\Microsoft.CodeCoverage.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.codecoverage\17.14.1\build\netstandard2.0\Microsoft.CodeCoverage.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.test.sdk\17.14.1\build\net8.0\Microsoft.NET.Test.Sdk.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.net.test.sdk\17.14.1\build\net8.0\Microsoft.NET.Test.Sdk.targets')" />
    <Import Project="$(NuGetPackageRoot)coverlet.collector\6.0.4\build\netstandard2.0\coverlet.collector.targets" Condition="Exists('$(NuGetPackageRoot)coverlet.collector\6.0.4\build\netstandard2.0\coverlet.collector.targets')" />
  </ImportGroup>
</Project>