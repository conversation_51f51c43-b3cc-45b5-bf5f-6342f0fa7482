{"version": 2, "dgSpecHash": "hm+eboJNzFI=", "success": true, "projectFilePath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Edge.Tests\\Miller.WMS.Edge.Tests.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\9.4.0\\aspire.dashboard.sdk.win-x64.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.elastic.clients.elasticsearch\\9.2.1-preview.1.25222.1\\aspire.elastic.clients.elasticsearch.9.2.1-preview.1.25222.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting\\9.4.0\\aspire.hosting.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.apphost\\9.4.0\\aspire.hosting.apphost.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure\\9.4.0\\aspire.hosting.azure.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.appconfiguration\\9.4.0\\aspire.hosting.azure.appconfiguration.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.appcontainers\\9.4.0\\aspire.hosting.azure.appcontainers.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.applicationinsights\\9.4.0\\aspire.hosting.azure.applicationinsights.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.cosmosdb\\9.4.0\\aspire.hosting.azure.cosmosdb.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.eventhubs\\9.4.0\\aspire.hosting.azure.eventhubs.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.keyvault\\9.4.0\\aspire.hosting.azure.keyvault.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.operationalinsights\\9.4.0\\aspire.hosting.azure.operationalinsights.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.postgresql\\9.4.0\\aspire.hosting.azure.postgresql.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.redis\\9.4.0\\aspire.hosting.azure.redis.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.search\\9.4.0\\aspire.hosting.azure.search.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.servicebus\\9.4.0\\aspire.hosting.azure.servicebus.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.sql\\9.4.0\\aspire.hosting.azure.sql.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.azure.storage\\9.4.0\\aspire.hosting.azure.storage.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.keycloak\\9.3.1-preview.1.25305.6\\aspire.hosting.keycloak.9.3.1-preview.1.25305.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.mongodb\\9.4.0\\aspire.hosting.mongodb.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.mysql\\9.4.0\\aspire.hosting.mysql.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.4.0\\aspire.hosting.orchestration.win-x64.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.postgresql\\9.4.0\\aspire.hosting.postgresql.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.rabbitmq\\9.4.0\\aspire.hosting.rabbitmq.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.redis\\9.4.0\\aspire.hosting.redis.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.sqlserver\\9.4.0\\aspire.hosting.sqlserver.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.testing\\9.4.0\\aspire.hosting.testing.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.npgsql.entityframeworkcore.postgresql\\9.4.0\\aspire.npgsql.entityframeworkcore.postgresql.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.azure.storage.blobs\\9.0.0\\aspnetcore.healthchecks.azure.storage.blobs.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.azure.storage.queues\\9.0.0\\aspnetcore.healthchecks.azure.storage.queues.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.cosmosdb\\9.0.0\\aspnetcore.healthchecks.cosmosdb.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.mongodb\\9.0.0\\aspnetcore.healthchecks.mongodb.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.mysql\\9.0.0\\aspnetcore.healthchecks.mysql.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.npgsql\\9.0.0\\aspnetcore.healthchecks.npgsql.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.rabbitmq\\9.0.0\\aspnetcore.healthchecks.rabbitmq.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.redis\\9.0.0\\aspnetcore.healthchecks.redis.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.sqlserver\\9.0.0\\aspnetcore.healthchecks.sqlserver.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.uris\\9.0.0\\aspnetcore.healthchecks.uris.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.47.0\\azure.core.1.47.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.14.2\\azure.identity.1.14.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning\\1.2.1\\azure.provisioning.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.appconfiguration\\1.1.0\\azure.provisioning.appconfiguration.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.appcontainers\\1.1.0\\azure.provisioning.appcontainers.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.applicationinsights\\1.1.0\\azure.provisioning.applicationinsights.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.containerregistry\\1.1.0\\azure.provisioning.containerregistry.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.cosmosdb\\1.0.0\\azure.provisioning.cosmosdb.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.eventhubs\\1.1.0\\azure.provisioning.eventhubs.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.keyvault\\1.1.0\\azure.provisioning.keyvault.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.operationalinsights\\1.1.0\\azure.provisioning.operationalinsights.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.postgresql\\1.1.1\\azure.provisioning.postgresql.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.redis\\1.1.0\\azure.provisioning.redis.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.search\\1.0.0\\azure.provisioning.search.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.servicebus\\1.1.0\\azure.provisioning.servicebus.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.sql\\1.1.0\\azure.provisioning.sql.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.provisioning.storage\\1.1.2\\azure.provisioning.storage.1.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.resourcemanager\\1.13.1\\azure.resourcemanager.1.13.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.resourcemanager.authorization\\1.1.4\\azure.resourcemanager.authorization.1.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.resourcemanager.keyvault\\1.3.2\\azure.resourcemanager.keyvault.1.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.resourcemanager.resources\\1.11.0\\azure.resourcemanager.resources.1.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.secrets\\4.8.0\\azure.security.keyvault.secrets.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.blobs\\12.24.1\\azure.storage.blobs.12.24.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.common\\12.23.0\\azure.storage.common.12.23.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.queues\\12.22.0\\azure.storage.queues.12.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.adminer\\9.7.0\\communitytoolkit.aspire.hosting.adminer.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.dbgate\\9.7.0\\communitytoolkit.aspire.hosting.dbgate.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.java\\9.7.0\\communitytoolkit.aspire.hosting.java.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.mongodb.extensions\\9.7.0\\communitytoolkit.aspire.hosting.mongodb.extensions.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.postgresql.extensions\\9.7.0\\communitytoolkit.aspire.hosting.postgresql.extensions.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.redis.extensions\\9.7.0\\communitytoolkit.aspire.hosting.redis.extensions.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.aspire.hosting.sqlserver.extensions\\9.7.0\\communitytoolkit.aspire.hosting.sqlserver.extensions.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\coverlet.collector\\6.0.4\\coverlet.collector.6.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.6.1\\dnsclient.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.aspire.hosting.elasticsearch\\9.3.0\\elastic.aspire.hosting.elasticsearch.9.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.clients.elasticsearch\\9.1.0\\elastic.clients.elasticsearch.9.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.transport\\0.9.2\\elastic.transport.0.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fractions\\7.3.0\\fractions.7.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.31.1\\google.protobuf.3.31.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.aspnetcore\\2.71.0\\grpc.aspnetcore.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.aspnetcore.server\\2.71.0\\grpc.aspnetcore.server.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.aspnetcore.server.clientfactory\\2.71.0\\grpc.aspnetcore.server.clientfactory.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core.api\\2.71.0\\grpc.core.api.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.client\\2.71.0\\grpc.net.client.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.clientfactory\\2.71.0\\grpc.net.clientfactory.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.common\\2.71.0\\grpc.net.common.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.tools\\2.72.0\\grpc.tools.2.72.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\json.more.net\\2.1.0\\json.more.net.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpatch.net\\3.3.0\\jsonpatch.net.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpointer.net\\5.2.0\\jsonpointer.net.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\kubernetesclient\\17.0.4\\kubernetesclient.17.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\2.5.192\\messagepack.2.5.192.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\2.5.192\\messagepack.annotations.2.5.192.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.azure.cosmos\\3.52.0\\microsoft.azure.cosmos.3.52.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.cryptography\\9.0.4\\microsoft.bcl.cryptography.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.0\\microsoft.bcl.hashcode.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codecoverage\\17.14.1\\microsoft.codecoverage.17.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\6.0.2\\microsoft.data.sqlclient.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\6.0.2\\microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.8\\microsoft.entityframeworkcore.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.8\\microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.8\\microsoft.entityframeworkcore.analyzers.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\9.0.7\\microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ambientmetadata.application\\9.7.0\\microsoft.extensions.ambientmetadata.application.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.8\\microsoft.extensions.caching.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.8\\microsoft.extensions.caching.memory.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.compliance.abstractions\\9.7.0\\microsoft.extensions.compliance.abstractions.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.8\\microsoft.extensions.configuration.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.8\\microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.8\\microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\9.0.8\\microsoft.extensions.configuration.commandline.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\9.0.8\\microsoft.extensions.configuration.environmentvariables.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.8\\microsoft.extensions.configuration.fileextensions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.8\\microsoft.extensions.configuration.json.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\9.0.8\\microsoft.extensions.configuration.usersecrets.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.8\\microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.8\\microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.autoactivation\\9.7.0\\microsoft.extensions.dependencyinjection.autoactivation.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\9.0.8\\microsoft.extensions.diagnostics.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.8\\microsoft.extensions.diagnostics.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.exceptionsummarization\\9.7.0\\microsoft.extensions.diagnostics.exceptionsummarization.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks\\9.0.7\\microsoft.extensions.diagnostics.healthchecks.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks.abstractions\\9.0.7\\microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks.entityframeworkcore\\9.0.7\\microsoft.extensions.diagnostics.healthchecks.entityframeworkcore.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.features\\8.0.18\\microsoft.extensions.features.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.8\\microsoft.extensions.fileproviders.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.8\\microsoft.extensions.fileproviders.physical.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.8\\microsoft.extensions.filesystemglobbing.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\9.0.8\\microsoft.extensions.hosting.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.8\\microsoft.extensions.hosting.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\9.0.7\\microsoft.extensions.http.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.diagnostics\\9.7.0\\microsoft.extensions.http.diagnostics.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.resilience\\9.7.0\\microsoft.extensions.http.resilience.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.8\\microsoft.extensions.logging.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.8\\microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.8\\microsoft.extensions.logging.configuration.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.8\\microsoft.extensions.logging.console.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.8\\microsoft.extensions.logging.debug.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\9.0.8\\microsoft.extensions.logging.eventlog.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\9.0.8\\microsoft.extensions.logging.eventsource.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\9.0.7\\microsoft.extensions.objectpool.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.8\\microsoft.extensions.options.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.8\\microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.8\\microsoft.extensions.primitives.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.resilience\\9.7.0\\microsoft.extensions.resilience.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.servicediscovery\\9.4.0\\microsoft.extensions.servicediscovery.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.servicediscovery.abstractions\\9.4.0\\microsoft.extensions.servicediscovery.abstractions.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.telemetry\\9.7.0\\microsoft.extensions.telemetry.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.telemetry.abstractions\\9.7.0\\microsoft.extensions.telemetry.abstractions.9.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.73.1\\microsoft.identity.client.4.73.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.73.1\\microsoft.identity.client.extensions.msal.4.73.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.5.0\\microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.5.0\\microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.5.0\\microsoft.identitymodel.logging.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.5.0\\microsoft.identitymodel.protocols.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.5.0\\microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.5.0\\microsoft.identitymodel.tokens.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.6.3\\microsoft.net.stringtools.17.6.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.test.sdk\\17.14.1\\microsoft.net.test.sdk.17.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testing.extensions.trxreport.abstractions\\1.7.3\\microsoft.testing.extensions.trxreport.abstractions.1.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testing.platform\\1.7.3\\microsoft.testing.platform.1.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testing.platform.msbuild\\1.7.3\\microsoft.testing.platform.msbuild.1.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.objectmodel\\17.14.1\\microsoft.testplatform.objectmodel.17.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.testhost\\17.14.1\\microsoft.testplatform.testhost.17.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading.only\\17.13.61\\microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.validation\\17.8.8\\microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\minver\\6.0.0\\minver.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\3.4.0\\mongodb.bson.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\3.4.0\\mongodb.driver.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysqlconnector\\2.3.1\\mysqlconnector.2.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nerdbank.streams\\2.12.87\\nerdbank.streams.2.12.87.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\9.0.3\\npgsql.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql.dependencyinjection\\9.0.3\\npgsql.dependencyinjection.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql.entityframeworkcore.postgresql\\9.0.4\\npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql.opentelemetry\\9.0.3\\npgsql.opentelemetry.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry\\1.12.0\\opentelemetry.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.api\\1.12.0\\opentelemetry.api.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.api.providerbuilderextensions\\1.12.0\\opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.exporter.opentelemetryprotocol\\1.12.0\\opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.extensions.hosting\\1.12.0\\opentelemetry.extensions.hosting.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.aspnetcore\\1.12.0\\opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.http\\1.12.0\\opentelemetry.instrumentation.http.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.runtime\\1.12.0\\opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.core\\8.6.2\\polly.core.8.6.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions\\8.6.2\\polly.extensions.8.6.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.ratelimiting\\8.4.2\\polly.ratelimiting.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rabbitmq.client\\7.1.2\\rabbitmq.client.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\semver\\3.0.0\\semver.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.30.1\\sharpcompress.0.30.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\snappier\\1.0.0\\snappier.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.8.41\\stackexchange.redis.2.8.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\streamjsonrpc\\2.22.11\\streamjsonrpc.2.22.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.5.0\\system.clientmodel.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\9.0.4\\system.configuration.configurationmanager.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.0\\system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.8\\system.diagnostics.eventlog.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.5.0\\system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\9.0.7\\system.io.hashing.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.7\\system.io.pipelines.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\8.0.1\\system.memory.data.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\8.0.0\\system.reflection.metadata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\9.0.4\\system.security.cryptography.pkcs.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\9.0.4\\system.security.cryptography.protecteddata.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.11\\system.text.json.6.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.ratelimiting\\8.0.0\\system.threading.ratelimiting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.analyzers\\1.23.0\\xunit.analyzers.1.23.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.runner.visualstudio\\3.1.3\\xunit.runner.visualstudio.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3\\3.0.0\\xunit.v3.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3.assert\\3.0.0\\xunit.v3.assert.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3.common\\3.0.0\\xunit.v3.common.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3.core\\3.0.0\\xunit.v3.core.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3.extensibility.core\\3.0.0\\xunit.v3.extensibility.core.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3.runner.common\\3.0.0\\xunit.v3.runner.common.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.v3.runner.inproc.console\\3.0.0\\xunit.v3.runner.inproc.console.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yamldotnet\\16.3.0\\yamldotnet.16.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zstdsharp.port\\0.7.3\\zstdsharp.port.0.7.3.nupkg.sha512"], "logs": []}