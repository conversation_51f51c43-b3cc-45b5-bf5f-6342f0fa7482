{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Miller.WMS.Edge.Web.styles.css", "AssetFile": "Miller.WMS.Edge.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000577367206"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=\""}, {"Name": "ETag", "Value": "W/\"bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY="}]}, {"Route": "Miller.WMS.Edge.Web.styles.css", "AssetFile": "Miller.WMS.Edge.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY="}]}, {"Route": "Miller.WMS.Edge.Web.styles.css.gz", "AssetFile": "Miller.WMS.Edge.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE="}]}, {"Route": "Miller.WMS.Edge.Web.ug9uonmnr1.styles.css", "AssetFile": "Miller.WMS.Edge.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000577367206"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=\""}, {"Name": "ETag", "Value": "W/\"bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ug9uonmnr1"}, {"Name": "integrity", "Value": "sha256-bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY="}, {"Name": "label", "Value": "Miller.WMS.Edge.Web.styles.css"}]}, {"Route": "Miller.WMS.Edge.Web.ug9uonmnr1.styles.css", "AssetFile": "Miller.WMS.Edge.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ug9uonmnr1"}, {"Name": "integrity", "Value": "sha256-bn/zkFNcgK0oUGZpOyE3bhXGXu2w1ldAL/RjO+iKADY="}, {"Name": "label", "Value": "Miller.WMS.Edge.Web.styles.css"}]}, {"Route": "Miller.WMS.Edge.Web.ug9uonmnr1.styles.css.gz", "AssetFile": "Miller.WMS.Edge.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ug9uonmnr1"}, {"Name": "integrity", "Value": "sha256-dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE="}, {"Name": "label", "Value": "Miller.WMS.Edge.Web.styles.css.gz"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000661375661"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "ETag", "Value": "W/\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw="}]}, {"Route": "app.d3h8l9wove.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000661375661"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "ETag", "Value": "W/\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3h8l9wove"}, {"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.d3h8l9wove.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3h8l9wove"}, {"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.d3h8l9wove.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3h8l9wove"}, {"Name": "integrity", "Value": "sha256-UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143389733"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030228832"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030228832"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163746520"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071058054"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071058054"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163746520"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "integrity", "Value": "sha256-k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143348624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030246204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030246204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163692912"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "integrity", "Value": "sha256-cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163692912"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000070952178"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000070952178"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143348624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "integrity", "Value": "sha256-vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143389733"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "integrity", "Value": "sha256-KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293513355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038887809"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038887809"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000312012480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079560824"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079560824"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000312012480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "integrity", "Value": "sha256-mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293513355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "integrity", "Value": "sha256-GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294464075"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038869670"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038869670"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000309023486"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066538026"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066538026"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000309023486"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "integrity", "Value": "sha256-ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294464075"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "integrity", "Value": "sha256-tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000081665986"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022590191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022590191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000081665986"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "integrity", "Value": "sha256-DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089653936"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040836328"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040836328"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089653936"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "integrity", "Value": "sha256-Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082000820"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022605000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022605000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082000820"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "integrity", "Value": "sha256-0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089823049"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040888089"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040888089"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089823049"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "integrity", "Value": "sha256-A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029611205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008702993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008702993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032129546"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "integrity", "Value": "sha256-sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032129546"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010874175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010874175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029701794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008704887"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008704887"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032109944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "integrity", "Value": "sha256-AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032109944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010885302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010885302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029701794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "integrity", "Value": "sha256-EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029611205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "integrity", "Value": "sha256-q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.iy2auvubsp.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018338866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "ETag", "Value": "W/\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iy2auvubsp"}, {"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.iy2auvubsp.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iy2auvubsp"}, {"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.iy2auvubsp.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iy2auvubsp"}, {"Name": "integrity", "Value": "sha256-PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018338866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "ETag", "Value": "W/\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.fxquxrv84i.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010836702"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "ETag", "Value": "W/\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fxquxrv84i"}, {"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.fxquxrv84i.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fxquxrv84i"}, {"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.fxquxrv84i.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fxquxrv84i"}, {"Name": "integrity", "Value": "sha256-xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010836702"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "ETag", "Value": "W/\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.252a5wndhh.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030353620"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "ETag", "Value": "W/\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "252a5wndhh"}, {"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.252a5wndhh.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "98301"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "252a5wndhh"}, {"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.252a5wndhh.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "252a5wndhh"}, {"Name": "integrity", "Value": "sha256-yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030353620"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "ETag", "Value": "W/\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "98301"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011557888"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "ETag", "Value": "W/\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.okq9zf051y.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011557888"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "ETag", "Value": "W/\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okq9zf051y"}, {"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.okq9zf051y.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okq9zf051y"}, {"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.okq9zf051y.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okq9zf051y"}, {"Name": "integrity", "Value": "sha256-T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.cwuvm2sdc3.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025834453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "ETag", "Value": "W/\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuvm2sdc3"}, {"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.cwuvm2sdc3.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "167417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuvm2sdc3"}, {"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.cwuvm2sdc3.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuvm2sdc3"}, {"Name": "integrity", "Value": "sha256-28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.ja11lcg8ur.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026306098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "ETag", "Value": "W/\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ja11lcg8ur"}, {"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.ja11lcg8ur.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "157798"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ja11lcg8ur"}, {"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.ja11lcg8ur.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ja11lcg8ur"}, {"Name": "integrity", "Value": "sha256-HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026306098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "ETag", "Value": "W/\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "157798"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015567352"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "ETag", "Value": "W/\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.wf6sfai52w.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015567352"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "ETag", "Value": "W/\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf6sfai52w"}, {"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.wf6sfai52w.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf6sfai52w"}, {"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.wf6sfai52w.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf6sfai52w"}, {"Name": "integrity", "Value": "sha256-aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035459735"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "ETag", "Value": "W/\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.eve9uzuztn.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017734270"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "ETag", "Value": "W/\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eve9uzuztn"}, {"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.eve9uzuztn.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eve9uzuztn"}, {"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.eve9uzuztn.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eve9uzuztn"}, {"Name": "integrity", "Value": "sha256-EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017734270"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "ETag", "Value": "W/\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.n5tfi6zt97.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035459735"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "ETag", "Value": "W/\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n5tfi6zt97"}, {"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.n5tfi6zt97.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "91531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n5tfi6zt97"}, {"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.n5tfi6zt97.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n5tfi6zt97"}, {"Name": "integrity", "Value": "sha256-Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025834453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "ETag", "Value": "W/\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "167417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.k72fsduyas.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015505318"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "ETag", "Value": "W/\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k72fsduyas"}, {"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.k72fsduyas.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k72fsduyas"}, {"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.k72fsduyas.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k72fsduyas"}, {"Name": "integrity", "Value": "sha256-ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015505318"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "ETag", "Value": "W/\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038043065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "ETag", "Value": "W/\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018008932"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "ETag", "Value": "W/\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.r37jpkscte.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018008932"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "ETag", "Value": "W/\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r37jpkscte"}, {"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.r37jpkscte.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r37jpkscte"}, {"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.r37jpkscte.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r37jpkscte"}, {"Name": "integrity", "Value": "sha256-E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.ze3dr5b7df.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038043065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "ETag", "Value": "W/\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze3dr5b7df"}, {"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.ze3dr5b7df.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "78215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze3dr5b7df"}, {"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.ze3dr5b7df.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:57:59 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze3dr5b7df"}, {"Name": "integrity", "Value": "sha256-Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}]}]}