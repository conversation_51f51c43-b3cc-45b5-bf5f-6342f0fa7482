using Miller.WMS.Core.CDCService.Models;

namespace Miller.WMS.Core.CDCService.Services;

/// <summary>
/// Service interface for syncing Organization data between PostgreSQL and Elasticsearch
/// </summary>
public interface IOrganizationSyncService
{
    /// <summary>
    /// Performs initial sync of all organizations from database to Elasticsearch
    /// </summary>
    Task InitialSyncAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Syncs a specific organization to Elasticsearch
    /// </summary>
    Task SyncOrganizationAsync(int organizationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes an organization from Elasticsearch
    /// </summary>
    Task RemoveOrganizationAsync(int organizationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the count of organizations in Elasticsearch
    /// </summary>
    Task<long> GetElasticsearchOrganizationCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the count of organizations in the database
    /// </summary>
    Task<int> GetDatabaseOrganizationCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if Elasticsearch index exists and creates it if not
    /// </summary>
    Task EnsureIndexExistsAsync(CancellationToken cancellationToken = default);
}
