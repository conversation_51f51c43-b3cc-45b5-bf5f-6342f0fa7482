{"Version": 1, "WorkspaceRootPath": "C:\\_\\<PERSON>_Github\\Miller.WMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FFC89C97-E406-86FD-4913-024DF28206AE}|Miller.WMS.Core.DataService\\Miller.WMS.Core.DataService.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.core.dataservice\\worker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFC89C97-E406-86FD-4913-024DF28206AE}|Miller.WMS.Core.DataService\\Miller.WMS.Core.DataService.csproj|solutionrelative:miller.wms.core.dataservice\\worker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.core.cdcservice\\worker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|solutionrelative:miller.wms.core.cdcservice\\worker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E5271F8-A733-45C9-B134-584133A30969}|Miller.WMS.Edge.AppHost\\Miller.WMS.Edge.AppHost.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.edge.apphost\\apphost.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E5271F8-A733-45C9-B134-584133A30969}|Miller.WMS.Edge.AppHost\\Miller.WMS.Edge.AppHost.csproj|solutionrelative:miller.wms.edge.apphost\\apphost.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.core.cdcservice\\services\\organizationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|solutionrelative:miller.wms.core.cdcservice\\services\\organizationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.core.cdcservice\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|solutionrelative:miller.wms.core.cdcservice\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{20B93718-07FD-1108-FCAD-13CE8456DD16}|Miller.WMS.Edge.Tests\\Miller.WMS.Edge.Tests.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.edge.tests\\simplecdctests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{20B93718-07FD-1108-FCAD-13CE8456DD16}|Miller.WMS.Edge.Tests\\Miller.WMS.Edge.Tests.csproj|solutionrelative:miller.wms.edge.tests\\simplecdctests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{20B93718-07FD-1108-FCAD-13CE8456DD16}|Miller.WMS.Edge.Tests\\Miller.WMS.Edge.Tests.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.edge.tests\\cdctests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{20B93718-07FD-1108-FCAD-13CE8456DD16}|Miller.WMS.Edge.Tests\\Miller.WMS.Edge.Tests.csproj|solutionrelative:miller.wms.edge.tests\\cdctests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Worker.cs", "DocumentMoniker": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Core.DataService\\Worker.cs", "RelativeDocumentMoniker": "Miller.WMS.Core.DataService\\Worker.cs", "ToolTip": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Core.DataService\\Worker.cs", "RelativeToolTip": "Miller.WMS.Core.DataService\\Worker.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAWwBMAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T12:43:50.954Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "OrganizationSyncService.cs", "DocumentMoniker": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Core.CDCService\\Services\\OrganizationSyncService.cs", "RelativeDocumentMoniker": "Miller.WMS.Core.CDCService\\Services\\OrganizationSyncService.cs", "ToolTip": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Core.CDCService\\Services\\OrganizationSyncService.cs", "RelativeToolTip": "Miller.WMS.Core.CDCService\\Services\\OrganizationSyncService.cs", "ViewState": "AgIAABgAAADAzMzMzEwswAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T12:38:41.375Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Program.cs", "DocumentMoniker": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Core.CDCService\\Program.cs", "RelativeDocumentMoniker": "Miller.WMS.Core.CDCService\\Program.cs", "ToolTip": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Core.CDCService\\Program.cs", "RelativeToolTip": "Miller.WMS.Core.CDCService\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T22:46:41.575Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Worker.cs", "DocumentMoniker": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Core.CDCService\\Worker.cs", "RelativeDocumentMoniker": "Miller.WMS.Core.CDCService\\Worker.cs", "ToolTip": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Core.CDCService\\Worker.cs", "RelativeToolTip": "Miller.WMS.Core.CDCService\\Worker.cs", "ViewState": "AgIAAA8AAAAAAAAAAAApwBoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T22:42:48.954Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "SimpleCDCTests.cs", "DocumentMoniker": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Tests\\SimpleCDCTests.cs", "RelativeDocumentMoniker": "Miller.WMS.Edge.Tests\\SimpleCDCTests.cs", "ToolTip": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Tests\\SimpleCDCTests.cs", "RelativeToolTip": "Miller.WMS.Edge.Tests\\SimpleCDCTests.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAgwJUAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T22:38:21.519Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CDCTests.cs", "DocumentMoniker": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Tests\\CDCTests.cs", "RelativeDocumentMoniker": "Miller.WMS.Edge.Tests\\CDCTests.cs", "ToolTip": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Tests\\CDCTests.cs", "RelativeToolTip": "Miller.WMS.Edge.Tests\\CDCTests.cs", "ViewState": "AgIAAJ8AAAAAAAAAAAAAALEAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T22:38:12.178Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AppHost.cs", "DocumentMoniker": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Edge.AppHost\\AppHost.cs", "RelativeDocumentMoniker": "Miller.WMS.Edge.AppHost\\AppHost.cs", "ToolTip": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Edge.AppHost\\AppHost.cs", "RelativeToolTip": "Miller.WMS.Edge.AppHost\\AppHost.cs", "ViewState": "AgIAAAcAAABAzczMzMwWwBkAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T22:37:51.032Z", "EditorCaption": ""}]}]}]}