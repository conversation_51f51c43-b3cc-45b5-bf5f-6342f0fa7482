{"Version": 1, "WorkspaceRootPath": "C:\\_\\<PERSON>_Github\\Miller.WMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.core.cdcservice\\worker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DDAFB011-116C-6929-0C37-929035D881D0}|Miller.WMS.Core.CDCService\\Miller.WMS.Core.CDCService.csproj|solutionrelative:miller.wms.core.cdcservice\\worker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Worker.cs", "DocumentMoniker": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Core.CDCService\\Worker.cs", "RelativeDocumentMoniker": "Miller.WMS.Core.CDCService\\Worker.cs", "ToolTip": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Core.CDCService\\Worker.cs", "RelativeToolTip": "Miller.WMS.Core.CDCService\\Worker.cs", "ViewState": "AgIAAA0AAAAANDMzMzPvvxYAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T13:01:38.873Z", "EditorCaption": ""}]}]}]}