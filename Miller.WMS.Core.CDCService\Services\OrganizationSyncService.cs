using Elastic.Clients.Elasticsearch;
using Microsoft.EntityFrameworkCore;
using Miller.WMS.Core.CDCService.Models;
using Miller.WMS.Domain;
using Miller.WMS.Shared.Data;

namespace Miller.WMS.Core.CDCService.Services;

/// <summary>
/// Service for syncing Organization data between PostgreSQL and Elasticsearch
/// </summary>
public class OrganizationSyncService : IOrganizationSyncService
{
    private readonly WmsContext _dbContext;
    private readonly ElasticsearchClient _elasticsearchClient;
    private readonly ILogger<OrganizationSyncService> _logger;
    private const string IndexName = "organizations";

    public OrganizationSyncService(
        WmsContext dbContext,
        ElasticsearchClient elasticsearchClient,
        ILogger<OrganizationSyncService> logger)
    {
        _dbContext = dbContext;
        _elasticsearchClient = elasticsearchClient;
        _logger = logger;
    }

    public async Task EnsureIndexExistsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var existsResponse = await _elasticsearchClient.Indices.ExistsAsync(IndexName, cancellationToken);

            if (!existsResponse.IsValidResponse)
            {
                _logger.LogError("Failed to check if Elasticsearch index exists: {Error}", existsResponse.DebugInformation);
                throw new InvalidOperationException($"Failed to check if Elasticsearch index exists: {existsResponse.DebugInformation}");
            }

            if (!existsResponse.Exists)
            {
                _logger.LogInformation("Creating Elasticsearch index: {IndexName}", IndexName);

                var createResponse = await _elasticsearchClient.Indices.CreateAsync(IndexName, c => c
                    .Mappings(m => m
                        .Properties<OrganizationSearchDocument>(p => p
                            .IntegerNumber(n => n.Id)
                            .Text(t => t.Name, td => td.Analyzer("standard"))
                            .Date(d => d.LastUpdated)
                            .IntegerNumber(n => n.FacilityCount)
                            .IntegerNumber(n => n.UserCount)
                            .Text(t => t.FacilityNames, td => td.Analyzer("standard"))
                        )
                    ), cancellationToken);

                if (!createResponse.IsValidResponse)
                {
                    _logger.LogError("Failed to create Elasticsearch index: {Error}", createResponse.DebugInformation);
                    throw new InvalidOperationException($"Failed to create Elasticsearch index: {createResponse.DebugInformation}");
                }

                _logger.LogInformation("Successfully created Elasticsearch index: {IndexName}", IndexName);
            }
            else
            {
                _logger.LogInformation("Elasticsearch index {IndexName} already exists", IndexName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring Elasticsearch index exists");
            throw;
        }
    }

    public async Task InitialSyncAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting initial sync of organizations to Elasticsearch");

        await EnsureIndexExistsAsync(cancellationToken);

        // Get all organizations with their related data
        var organizations = await _dbContext.Organizations
            .Include(o => o.Facilities)
            .Include(o => o.Users)
            .ToListAsync(cancellationToken);

        _logger.LogInformation("Found {Count} organizations to sync", organizations.Count);

        if (organizations.Count == 0)
        {
            _logger.LogInformation("No organizations found to sync");
            return;
        }

        // Convert to search documents
        var searchDocuments = organizations.Select(OrganizationSearchDocument.FromDomain).ToList();

        // Bulk index to Elasticsearch
        var bulkResponse = await _elasticsearchClient.BulkAsync(b => b
            .Index(IndexName)
            .IndexMany(searchDocuments, (descriptor, doc) => descriptor.Id(doc.Id))
        , cancellationToken);

        if (!bulkResponse.IsValidResponse)
        {
            _logger.LogError("Failed to bulk index organizations: {Error}", bulkResponse.DebugInformation);
            throw new InvalidOperationException($"Failed to bulk index organizations: {bulkResponse.DebugInformation}");
        }

        _logger.LogInformation("Successfully synced {Count} organizations to Elasticsearch", organizations.Count);
    }

    public async Task SyncOrganizationAsync(int organizationId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Syncing organization {OrganizationId} to Elasticsearch", organizationId);

        var organization = await _dbContext.Organizations
            .Include(o => o.Facilities)
            .Include(o => o.Users)
            .FirstOrDefaultAsync(o => o.Id == organizationId, cancellationToken);

        if (organization == null)
        {
            _logger.LogWarning("Organization {OrganizationId} not found in database", organizationId);
            await RemoveOrganizationAsync(organizationId, cancellationToken);
            return;
        }

        var searchDocument = OrganizationSearchDocument.FromDomain(organization);

        var indexResponse = await _elasticsearchClient.IndexAsync(searchDocument, IndexName, organizationId.ToString(), cancellationToken);

        if (!indexResponse.IsValidResponse)
        {
            _logger.LogError("Failed to index organization {OrganizationId}: {Error}", organizationId, indexResponse.DebugInformation);
            throw new InvalidOperationException($"Failed to index organization {organizationId}: {indexResponse.DebugInformation}");
        }

        _logger.LogInformation("Successfully synced organization {OrganizationId} to Elasticsearch", organizationId);
    }

    public async Task RemoveOrganizationAsync(int organizationId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Removing organization {OrganizationId} from Elasticsearch", organizationId);

        var deleteResponse = await _elasticsearchClient.DeleteAsync<OrganizationSearchDocument>(IndexName, organizationId.ToString(), cancellationToken);

        if (!deleteResponse.IsValidResponse && deleteResponse.Result != Result.NotFound)
        {
            _logger.LogError("Failed to delete organization {OrganizationId}: {Error}", organizationId, deleteResponse.DebugInformation);
            throw new InvalidOperationException($"Failed to delete organization {organizationId}: {deleteResponse.DebugInformation}");
        }

        _logger.LogInformation("Successfully removed organization {OrganizationId} from Elasticsearch", organizationId);
    }

    public async Task<long> GetElasticsearchOrganizationCountAsync(CancellationToken cancellationToken = default)
    {
        var countResponse = await _elasticsearchClient.CountAsync<OrganizationSearchDocument>(c => c
            .Indices(IndexName)
        , cancellationToken);

        if (!countResponse.IsValidResponse)
        {
            _logger.LogError("Failed to get Elasticsearch organization count: {Error}", countResponse.DebugInformation);
            return 0;
        }

        return countResponse.Count;
    }

    public async Task<int> GetDatabaseOrganizationCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Organizations.CountAsync(cancellationToken);
    }
}
