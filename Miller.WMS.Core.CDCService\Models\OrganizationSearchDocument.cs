namespace Miller.WMS.Core.CDCService.Models;

/// <summary>
/// Elasticsearch document model for Organization data
/// </summary>
public class OrganizationSearchDocument
{
    /// <summary>
    /// Organization ID (primary key from database)
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Organization name for searching
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when this document was last updated in Elasticsearch
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Number of facilities associated with this organization
    /// </summary>
    public int FacilityCount { get; set; }

    /// <summary>
    /// Number of users associated with this organization
    /// </summary>
    public int UserCount { get; set; }

    /// <summary>
    /// Facility names for enhanced searching
    /// </summary>
    public List<string> FacilityNames { get; set; } = new();

    /// <summary>
    /// Creates an OrganizationSearchDocument from a Domain Organization
    /// </summary>
    public static OrganizationSearchDocument FromDomain(Miller.WMS.Domain.Organization organization)
    {
        return new OrganizationSearchDocument
        {
            Id = organization.Id,
            Name = organization.Name,
            LastUpdated = DateTime.UtcNow,
            FacilityCount = organization.Facilities?.Count ?? 0,
            UserCount = organization.Users?.Count ?? 0,
            FacilityNames = organization.Facilities?.Select(f => f.Name).ToList() ?? new List<string>()
        };
    }
}
